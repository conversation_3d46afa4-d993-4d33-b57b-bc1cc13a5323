package com.center.huatuo.auth.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class LoginInReq {
  @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "01234567891")
  @NotBlank(message = "手机号不能为空")
  private String phoneNumber;

  @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
  @NotBlank(message = "密码不能为空")
  private String loginPassword;
}
