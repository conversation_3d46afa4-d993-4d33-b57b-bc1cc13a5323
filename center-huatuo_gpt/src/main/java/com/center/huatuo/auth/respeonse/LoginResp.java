package com.center.huatuo.auth.respeonse;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class LoginResp {

  @Schema(description = "Token", example = "1feaab34fde94f58a461b9fbd30161be")
  private String token;

  @Schema(description = "用户ID", example = "1")
  private Long userId;

  @Schema(description = "身份证号", example = "341333333333333333")
  private String identityNumber;


}
