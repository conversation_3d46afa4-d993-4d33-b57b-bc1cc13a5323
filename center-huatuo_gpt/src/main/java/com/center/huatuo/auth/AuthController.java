package com.center.huatuo.auth;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.extra.servlet.ServletUtil;

import com.center.cache.factory.CacheFactory;
import com.center.cache.interfaces.Cache;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.web.jwt.JwtTokenProvider;
import com.center.framework.web.pojo.CommonResult;
import com.center.huatuo.auth.request.LoginInReq;
import com.center.huatuo.auth.respeonse.LoginResp;
import com.center.infrastructure.system.biz.user.persistence.QUserModel;
import com.center.infrastructure.system.biz.user.pojo.UserInfo;
import com.center.infrastructure.system.biz.user.service.UserService;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.INACTIVE_OBJECT;
import static com.center.framework.web.pojo.CommonResult.success;

@Tag(name = "管理后台 - 用户认证")
@RestController
@RequestMapping("/admin/auth")
@Validated
public class AuthController {

    @Resource
    JwtTokenProvider jwtTokenProvider;

    @Resource
    private UserService userService;

    @Resource
    private JPAQueryFactory jpaQueryFactory;
    @Resource
    private CacheFactory cacheFactory;

    @Value("${center.jwt.expire-in}")
    private Long expireIn;

    @Operation(summary = "用户登录接口")
    @ResponseBody
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public CommonResult<LoginResp> load(@Valid @RequestBody LoginInReq loginInReq,
                                        HttpServletRequest request) {
        QUserModel qUserModel = QUserModel.userModel;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qUserModel.phoneNumber.eq(loginInReq.getPhoneNumber()));
        builder.and(qUserModel.password.eq(SecureUtil.md5(loginInReq.getLoginPassword())));
        UserInfo userInfo = jpaQueryFactory.select(Projections.bean(
                        UserInfo.class,
                        qUserModel.id,
                        qUserModel.username,
                        qUserModel.displayName,
                        qUserModel.departId,
                        qUserModel.tenantId,
                        qUserModel.phoneNumber,
                        qUserModel.email,
                        qUserModel.status,
                        qUserModel.identityNumber,
                        qUserModel.userMaritalStatus,
                        qUserModel.userLastName,
                        qUserModel.userFirstName
                ))
                .from(qUserModel)
                .where(builder)
                .fetchFirst();
        if (userInfo == null) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_USERNAME_PASSWORD, "用户名或密码错误");
        } else {
            if (!userInfo.getStatus().equals(CommonStatusEnum.ACTIVE)) {
                throw ServiceExceptionUtil.exception(INACTIVE_OBJECT, "此用户已停用");
            }
        }
        LoginContextHolder.setLoginUserId(userInfo.getId());
        LoginContextHolder.setLoginUserIdentityNumber(userInfo.getIdentityNumber());
        userService.updateUserLoginInfo(userInfo.getId(), ServletUtil.getClientIP(request));
        String token = jwtTokenProvider.createToken(userInfo.getId(), userInfo.getTenantId());
        Cache cache = cacheFactory.getHashCache();
        cache.put(LoginContextHolder.getLoginUserId().toString(), userInfo, expireIn);
        return success(new LoginResp(token, userInfo.getId(),  userInfo.getIdentityNumber()));
    }
}
