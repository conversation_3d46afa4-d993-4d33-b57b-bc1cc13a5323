package com.center.huatuo.report.service;

import com.center.huatuo.report.pojo.*;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface ReportService {
    /**
     * 查询问诊报告
     *
     * @param appointmentId-挂号id
     * @return 问诊报告信息
     */
    ReportResp getReport(Long appointmentId);

    /**
     * 更新问诊报告信息
     *
     * @param reportUpdateReq-更新信息
     */
    void updateReport(ReportUpdateReq reportUpdateReq);

    /**
     * 发送问诊报告,仅修改报告状态
     *
     * @param reportId-问诊报告id
     */
    void sendReport(Long reportId);

    /**
     * 删除问诊报告
     * @param id-问诊报告id
     */
    void deleteReport(Long id);

    List<FileUploadResp> uploadFile(List<MultipartFile> file, Long reportId);

    ResponseEntity<ByteArrayResource> previewFile(String filePath) throws IOException;

    List<ReportListResp> listReports(Integer isAttended);

    void deleteFile(Long fileId, String filePath);

    /**
     * 获取问诊报告id
     * @param appointmentId-挂号id
     * @return 问诊报告id
     */
    Long getReportId(Long appointmentId);

    List<FileUploadResp> uploadFileInChat(List<MultipartFile> file, Long sessionId);
}
