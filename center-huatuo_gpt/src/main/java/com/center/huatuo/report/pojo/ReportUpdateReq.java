package com.center.huatuo.report.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class ReportUpdateReq {
    @Schema(description = "问诊报告id")
    @NotNull(message = "问诊报告ID不能为空")
    private Long id;

    @Schema(description = "主诉")
    @NotEmpty(message = "主诉内容不能为空")
    private String chiefComplaint;

    @Schema(description = "现病史")
    private String presentIllness;

    @Schema(description = "既往史")
    private String pastMedicalHistory;

    @Schema(description = "家族史")
    private String familyMedicalHistory;

    @Schema(description = "过敏史")
    private String allergyHistory;
}
