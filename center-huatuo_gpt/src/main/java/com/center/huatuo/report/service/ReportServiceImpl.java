package com.center.huatuo.report.service;

import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.db.config.SnowFlakeConfig;
import com.center.framework.storage.factory.FileStorageServiceFactory;
import com.center.framework.storage.interfaces.FileStorageService;
import com.center.huatuo.common.enumeration.HuatuoAppointmentStatusEnum;
import com.center.huatuo.inquiry.appointment.persistence.HuatuoAppointmentsModel;
import com.center.huatuo.inquiry.appointment.persistence.HuatuoAppointmentsRepository;
import com.center.huatuo.inquiry.appointment.persistence.QHuatuoAppointmentsModel;
import com.center.huatuo.report.enumerate.ReportEnum;
import com.center.huatuo.report.persistence.*;
import com.center.huatuo.report.pojo.FileMessage;
import com.center.huatuo.report.pojo.FileUploadResp;
import com.center.huatuo.report.pojo.ReportListResp;
import com.center.huatuo.report.pojo.ReportResp;
import com.center.huatuo.report.pojo.ReportUpdateReq;
import com.center.infrastructure.system.biz.user.persistence.QUserModel;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.querydsl.core.types.dsl.Expressions.stringTemplate;

@Service
@Slf4j
public class ReportServiceImpl implements ReportService {

    @Resource
    private ReportRepository reportRepository;

    @Resource
    private FileAttachmentsRepository fileAttachmentsRepository;
    @Resource
    private JPAQueryFactory queryFactory;

    @Value("${preview.url}")
    private String previewUrl;

    @Resource
    private SnowFlakeConfig snowFlakeConfig;

    private final FileStorageService fileStorageService;
    @Autowired
    private HuatuoAppointmentsRepository huatuoAppointmentsRepository;

    @Autowired
    public ReportServiceImpl(FileStorageServiceFactory fileStorageServiceFactory) {
        this.fileStorageService = fileStorageServiceFactory.getFileStorageService();
    }


    @Override
    public ReportResp getReport(Long appointmentId) {
        List<ReportModel> reportModel = reportRepository.findAllByAppointmentId(appointmentId);
        if (reportModel.isEmpty()) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "问诊报告不存在");
        }
        QReportModel qReportModel = QReportModel.reportModel;
        QHuatuoAppointmentsModel qHuatuoAppointmentsModel = QHuatuoAppointmentsModel.huatuoAppointmentsModel;
        QUserModel qUserModel = QUserModel.userModel;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qReportModel.id.eq(qHuatuoAppointmentsModel.latestMedicalRecordsId));
        builder.and(qHuatuoAppointmentsModel.id.eq(appointmentId));
        builder.and(qUserModel.id.eq(qHuatuoAppointmentsModel.userId));
        ReportResp reportResp = queryFactory.select((Projections.bean(
                        ReportResp.class,
                        qReportModel.id,
                        qReportModel.appointmentId,
                        qReportModel.chiefComplaint,
                        qReportModel.presentIllness,
                        qReportModel.pastMedicalHistory,
                        qReportModel.familyMedicalHistory,
                        qReportModel.allergyHistory,
                        qReportModel.medicalRecordsStatus,
                        qUserModel.displayName.as("patientName"),
                        qHuatuoAppointmentsModel.departmentsName,
                        qHuatuoAppointmentsModel.doctorName,
                        qHuatuoAppointmentsModel.registrationType,
                        qHuatuoAppointmentsModel.doctorLevel,
                        qHuatuoAppointmentsModel.appointmentDate,
                        qHuatuoAppointmentsModel.appointmentStartTime,
                        qHuatuoAppointmentsModel.appointmentEndTime,
                        qHuatuoAppointmentsModel.visitingTime
                )))
                .from(qUserModel, qReportModel, qHuatuoAppointmentsModel)
                .where(builder)
                .fetchFirst();
        if(reportResp == null){
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,"报告不存在");
        }
        List<FileAttachmentsModel> fileList = fileAttachmentsRepository.findByReportId(reportResp.getId());
        if (CollectionUtils.isNotEmpty(fileList)) {
            List<FileMessage> fileMessageList = fileList.stream().map(
                    file -> {
                        FileMessage fileMessage = new FileMessage();
                        fileMessage.setFileId(file.getId());
                        fileMessage.setFilePath(file.getFilePath());
                        return fileMessage;
                    }
            ).collect(Collectors.toList());
            reportResp.setFileList(fileMessageList);
        }
        return reportResp;
    }

    @Override
    public void updateReport(ReportUpdateReq reportUpdateReq) {
        ReportModel reportModel = reportRepository.findById(reportUpdateReq.getId()).orElseThrow(
                () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "问诊报告不存在"));
        if (ReportEnum.PENDING.equals(reportModel.getMedicalRecordsStatus())) {
            reportRepository.save(OrikaUtils.convert(reportUpdateReq, ReportModel.class));
        } else {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "问诊报告状态错误");
        }
    }

    @Override
    public void sendReport(Long reportId) {
        ReportModel reportModel = reportRepository.findById(reportId).orElseThrow(
                () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "问诊报告不存在"));
        if (ReportEnum.PENDING.equals(reportModel.getMedicalRecordsStatus())) {
            reportModel.setMedicalRecordsStatus(ReportEnum.SENT);
        } else {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "问诊报告状态有误");
        }
        reportRepository.save(reportModel);
    }

    @Override
    public void deleteReport(Long id) {
        ReportModel reportModel = reportRepository.findById(id).orElseThrow(
                () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "问诊报告不存在"));
        if (reportModel.getMedicalRecordsStatus().equals(ReportEnum.SENT)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "已发送的问诊报告不能删除");
        } else if (reportModel.getMedicalRecordsStatus().equals(ReportEnum.EVALUATED)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "已评估的问诊报告不能删除");
        }
        try {
            HuatuoAppointmentsModel appointmentsModel = huatuoAppointmentsRepository.findById(reportModel.getAppointmentId()).orElseThrow(
                    () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "问诊报告对应的挂号记录不存在"));
            appointmentsModel.setLatestMedicalRecordsId(null);
            huatuoAppointmentsRepository.save(appointmentsModel);
        } catch (Exception e) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "删除问诊报告失败");
        }
    }

    @Override
    @Transactional
    public List<FileUploadResp> uploadFile(List<MultipartFile> filesList, Long reportId) {
        long fileNum = fileAttachmentsRepository.countByReportId(reportId);
        if (fileNum + filesList.size() > 9) {
            throw  ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "上传文件数量超出限制");
        }

        List<FileAttachmentsModel> fileAttachmentsModels = new ArrayList<>();
        List<FileUploadResp> fileUploadResps = new ArrayList<>();
        for (MultipartFile file : filesList) {
            Long id = snowFlakeConfig.snowFlakeCore().nextId();
            String targetPath = reportId + "/" + id + '/' + file.getOriginalFilename();
            try (InputStream inputStream = file.getInputStream()) {
                fileStorageService.uploadFile(targetPath, inputStream, true);
            } catch (IOException e) {
                log.error("文件上传失败：{}", targetPath, e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件上传失败：" + e.getMessage());
            }

            FileAttachmentsModel fileAttachmentsModel = new FileAttachmentsModel();
            fileAttachmentsModel.setId(id);
            fileAttachmentsModel.setFilePath(targetPath);
            fileAttachmentsModel.setReportId(reportId);
            fileAttachmentsModel.setFileName(file.getOriginalFilename());
            fileAttachmentsModel.setFileType(file.getContentType());
            fileAttachmentsModel.setUploadTime(LocalDateTime.now());
            fileAttachmentsModels.add(fileAttachmentsModel);

            FileUploadResp fileUploadResp = new FileUploadResp();
            fileUploadResp.setFileId(id);
            fileUploadResp.setFilePath(targetPath);
            fileUploadResps.add(fileUploadResp);
        }
        fileAttachmentsRepository.saveAll(fileAttachmentsModels);
        return fileUploadResps;
    }


    @Override
    @Transactional
    public List<FileUploadResp> uploadFileInChat(List<MultipartFile> filesList, Long sessionId) {
        long fileNum = fileAttachmentsRepository.countBySessionId(sessionId);
        if (fileNum + filesList.size() > 9) {
            throw  ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "上传文件数量超出限制");
        }

        List<FileAttachmentsModel> fileAttachmentsModels = new ArrayList<>();
        List<FileUploadResp> fileUploadResps = new ArrayList<>();
        for (MultipartFile file: filesList ) {
            Long id = snowFlakeConfig.snowFlakeCore().nextId();
            String targetPath = sessionId + "/" + id + '/' + file.getOriginalFilename();
            try (InputStream inputStream = file.getInputStream()) {
                fileStorageService.uploadFile(targetPath, inputStream, true);
            } catch (IOException e) {
                log.error("文件上传失败：{}", targetPath, e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件上传失败：" + e.getMessage());
            }
            FileAttachmentsModel fileAttachmentsModel = new FileAttachmentsModel();
            fileAttachmentsModel.setId(id);
            fileAttachmentsModel.setFilePath(targetPath);
            fileAttachmentsModel.setSessionId(sessionId);
            fileAttachmentsModel.setFileName(file.getOriginalFilename());
            fileAttachmentsModel.setFileType(file.getContentType());
            fileAttachmentsModel.setUploadTime(LocalDateTime.now());
            fileAttachmentsModels.add(fileAttachmentsModel);

            FileUploadResp fileUploadResp = new FileUploadResp();
            fileUploadResp.setFileId(id);
            fileUploadResp.setFilePath(targetPath);
            fileUploadResps.add(fileUploadResp);
        }
        fileAttachmentsRepository.saveAll(fileAttachmentsModels);
        return fileUploadResps;
    }

    @Override
    public ResponseEntity<ByteArrayResource> previewFile(String filePath) throws IOException {
        log.info("开始预览");
        return fileStorageService.previewFile(filePath);
    }

    @Override
    public List<ReportListResp> listReports(Integer isAttended) {
        List<ReportListResp> resultList = new ArrayList<>();
        QReportModel qReportModel = QReportModel.reportModel;
        QHuatuoAppointmentsModel qHuatuoAppointmentsModel = QHuatuoAppointmentsModel.huatuoAppointmentsModel;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qHuatuoAppointmentsModel.userId.eq(LoginContextHolder.getLoginUserId()));
        builder.and(qReportModel.id.eq(qHuatuoAppointmentsModel.latestMedicalRecordsId));
        if (isAttended == 0) {
            builder.and(qHuatuoAppointmentsModel.status.eq(HuatuoAppointmentStatusEnum.UNATTENDED));
        } else if (isAttended == 1) {
            builder.and(qHuatuoAppointmentsModel.status.eq(HuatuoAppointmentStatusEnum.ATTENDED));
        }
        queryFactory.select((Projections.bean(
                        ReportListResp.class,
                        qReportModel.id,
                        qHuatuoAppointmentsModel.id.as("appointmentId"),
                        qHuatuoAppointmentsModel.departmentsName,
                        qHuatuoAppointmentsModel.doctorName,
                        qHuatuoAppointmentsModel.doctorLevel,
                        qReportModel.medicalRecordsStatus,
                        qHuatuoAppointmentsModel.appointmentDate,
                        stringTemplate("CONCAT(DATE_FORMAT({0}, '%H:%i'), '~', DATE_FORMAT({1}, '%H:%i'))",
                                qHuatuoAppointmentsModel.appointmentStartTime,
                                qHuatuoAppointmentsModel.appointmentEndTime).as("appointmentTime")
                )))
                .from(qReportModel, qHuatuoAppointmentsModel)
                .where(builder)
                .orderBy(qHuatuoAppointmentsModel.appointmentDate.asc(),qHuatuoAppointmentsModel.appointmentStartTime.asc())
                .fetch().forEach(resultList::add);

        return resultList;

    }

    @Override
    @Transactional
    public void deleteFile(Long fileId, String filePath) {
        try {
            fileStorageService.deleteFile(filePath);
        } catch (IOException e) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DELETE_OBJECT_ERROR, "附件删除失败");
        }
        fileAttachmentsRepository.deleteById(fileId);
    }

    @Override
    public Long getReportId(Long appointmentId) {
        HuatuoAppointmentsModel appointmentModel = huatuoAppointmentsRepository.findById(appointmentId).orElseThrow(
                () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "挂号记录不存在"));
        return appointmentModel.getLatestMedicalRecordsId();
    }

}
