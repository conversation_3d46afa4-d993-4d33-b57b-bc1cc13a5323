package com.center.huatuo.report.persistence;

import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReportRepository extends JoinFetchCapableQueryDslJpaRepository<ReportModel, Long> {


    List<ReportModel> findAllByAppointmentId(Long appointmentId);
    ReportModel findByAppointmentIdAndId(Long appointmentId,Long id);
}
