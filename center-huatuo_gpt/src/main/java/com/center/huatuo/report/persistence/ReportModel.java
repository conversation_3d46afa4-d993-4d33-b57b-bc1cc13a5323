package com.center.huatuo.report.persistence;

import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import com.center.huatuo.report.enumerate.ReportEnum;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;

@Data
@Table(name = "huatuo_medical_records")
@Entity
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class ReportModel extends BaseTenantModel {

    @Column(name = "appointment_id",nullable = false)
    private Long appointmentId ;

    @Column(name = "chief_complaint")
    private String chiefComplaint;

    @Column(name = "present_illness")
    private String presentIllness;

    @Column(name = "past_medical_history")
    private String pastMedicalHistory;

    @Column(name = "family_medical_history")
    private String familyMedicalHistory;

    @Column(name = "allergy_history")
    private String allergyHistory;

    @Column(name = "medical_records_status")
    @Enumerated(EnumType.STRING)
    private ReportEnum medicalRecordsStatus;

    @Column(name = "session_id")
    private Long medicalRecordsSessionId;
}
