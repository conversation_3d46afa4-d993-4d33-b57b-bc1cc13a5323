package com.center.huatuo.report.persistence;

import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;

import java.util.List;

public interface FileAttachmentsRepository extends JoinFetchCapableQueryDslJpaRepository<FileAttachmentsModel, Long> {
    List<FileAttachmentsModel> findByReportId(Long reportId);

    List<FileAttachmentsModel> findBySessionId(Long sessionId);

    long countBySessionId(Long sessionId);

    long countByReportId(Long reportId);
}
