package com.center.huatuo.report.enumerate;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum ReportEnum implements IEnumerate<String> {
    PENDING("PENDING","未发送"),
    SENT("SENT","已发送"),
    EVALUATED("EVALUATED","已评价"),
    LOADING("LOADING","生成中"),;

    private String value;

    private String description;

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
