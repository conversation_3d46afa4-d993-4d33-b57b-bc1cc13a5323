package com.center.huatuo.report.persistence;

import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description: 附件实体类
 * @date 2024/12/9 13:37
 */
@Data
@Table(name = "huatuo_file_attachments")
@Entity
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class FileAttachmentsModel extends BaseTenantModel {

    @Column(name = "file_path",nullable = false)
    private String filePath;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "file_type")
    private String fileType;

    @Column(name = "upload_time")
    private LocalDateTime uploadTime;

    @Column(name = "report_id")
    private Long reportId;

    @Column(name = "session_id")
    private Long sessionId;
}
