package com.center.huatuo.report;

import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.pojo.CommonResult;
import com.center.huatuo.report.pojo.FileUploadResp;
import com.center.huatuo.report.pojo.ReportListResp;
import com.center.huatuo.report.pojo.ReportResp;
import com.center.huatuo.report.pojo.ReportUpdateReq;
import com.center.huatuo.report.service.ReportService;
import com.netease.yidun.sdk.core.utils.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@Tag(name = "问诊报告")
@RequestMapping("/report")
@Validated
public class ReportController {
    @Resource
    private ReportService reportService;

    @EnumConvertPoint
    @Operation(summary = "查询问诊报告")
    @Parameter(description = "挂号ID")
    @GetMapping("/get_report")
    public CommonResult<ReportResp> getReport(@RequestParam Long appointmentId) {
        log.info("查询问诊报告，挂号id为:{}", appointmentId);
        return CommonResult.success(reportService.getReport(appointmentId));
    }

    @Operation(summary = "修改问诊报告")
    @Parameter(description = "问诊报告ID")
    @PostMapping("/update_report")
    public CommonResult<String> updateReport(@RequestBody @Valid ReportUpdateReq reportUpdateReq) {
        log.info("修改问诊报告,{}", reportUpdateReq);
        reportService.updateReport(reportUpdateReq);
        return CommonResult.successWithMessageOnly("修改成功");
    }

    @Operation(summary = "发送问诊报告")
    @Parameter(description = "问诊报告ID")
    @PostMapping("/send_report")
    public CommonResult<String> sendReport(@RequestParam Long id) {
        log.info("发送问诊报告，问诊报告id为:{}", id);
        reportService.sendReport(id);
        return CommonResult.successWithMessageOnly("发送成功");
    }

    @Operation(summary = "删除问诊报告")
    @Parameter(description = "问诊报告ID")
    @PostMapping("/delete_report")
    public CommonResult<String> deleteReport(@RequestParam Long id) {
        log.info("删除问诊报告，问诊报告id为:{}", id);
        reportService.deleteReport(id);
        return CommonResult.successWithMessageOnly("删除成功");
    }

    @Operation(summary = "查看报告时上传文件")
    @PostMapping("/upload_file")
    public CommonResult<List<FileUploadResp>> uploadFile(@RequestParam List<MultipartFile> file, @RequestParam Long reportId) {
        return CommonResult.success(reportService.uploadFile(file, reportId), "附件上传成功");
    }

    @Operation(summary = "对话过程中批量上传文件")
    @PostMapping("/upload_file_in_chat")
    public CommonResult<List<FileUploadResp>> uploadFileInChat(@RequestParam List<MultipartFile> file, @RequestParam Long sessionId) {
        return CommonResult.success(reportService.uploadFileInChat(file, sessionId), "附件上传成功");
    }

    @Operation(summary = "删除文件")
    @PostMapping("/delete_file")
    public CommonResult<String> deleteFile(@RequestParam Long fileId, @RequestParam String filePath) {
        reportService.deleteFile(fileId,filePath);
        return CommonResult.successWithMessageOnly("附件删除成功");
    }

    @Operation(summary = "预览文件")
    @Parameter(description = "文件路径")
    @GetMapping("/preview_file")
    public ResponseEntity<ByteArrayResource> previewFile(@RequestParam String filePath) {
        if (StringUtils.isBlank(filePath)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILE_NOT_FOUND, "文件路径");
        }
        try {
            return reportService.previewFile(filePath);
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @EnumConvertPoint
    @Operation(summary = "查询问诊记录")
    @Parameter(name = "isAttended", description = "是否就诊，0表示未就诊，1表示已就诊")
    @GetMapping("/list_reports")
    public CommonResult<List<ReportListResp>> listReports(@RequestParam Integer isAttended) {
        return CommonResult.success(reportService.listReports(isAttended));
    }

    @Operation(summary = "查询是否有问诊报告并返回报告id,没有返回null")
    @GetMapping("/get_report_id")
    public CommonResult<Long> getReportId(@RequestParam Long appointmentId) {
        return CommonResult.success(reportService.getReportId(appointmentId));
    }
}
