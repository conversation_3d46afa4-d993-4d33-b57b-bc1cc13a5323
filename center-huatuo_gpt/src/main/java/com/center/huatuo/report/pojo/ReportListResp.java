package com.center.huatuo.report.pojo;

import com.center.framework.common.annotation.enumvalidate.EnumValidate;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.center.huatuo.report.enumerate.ReportEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

@Data
public class ReportListResp {
    @Schema(description = "问诊报告id")
    private Long id;

    @Schema(description = "挂号记录id")
    private Long appointmentId;

    @Schema(description = "科室")
    private String departmentsName;

    @Schema(description = "医生名字")
    private String doctorName;

    @Schema(description = "医生级别")
    private String doctorLevel;

    @Schema(description = "报告状态")
    @EnumValidate(message = "报告状态不正确",value = ReportEnum.class)
    private ReportEnum medicalRecordsStatus;

    @EnumConvert(value = ReportEnum.class,srcFieldName = "medicalRecordsStatus")
    @Schema(description = "报告状态名")
    private String reportStatusName;

    @Schema(description = "挂号日期")
    private LocalDate appointmentDate;

    @Schema(description = "挂号时间")
    private String appointmentTime;
}
