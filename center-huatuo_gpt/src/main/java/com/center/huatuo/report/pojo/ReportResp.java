package com.center.huatuo.report.pojo;

import com.center.framework.common.annotation.enumvalidate.EnumValidate;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.center.huatuo.report.enumerate.ReportEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Data
public class ReportResp {
    @Schema(description = "问诊报告id")
    private Long id;

    @Schema(description = "挂号id")
    private Long appointmentId ;

    @Schema(description = "患者名字")
    private String patientName;

    @Schema(description = "科室")
    private String departmentsName;

    @Schema(description = "医生名字")
    private String doctorName;

    @Schema(description = "医生级别")
    private String doctorLevel;

    @Schema(description = "挂号类型")
    private String registrationType;

    @Schema(description = "主诉")
    private String chiefComplaint;

    @Schema(description = "现病史")
    private String presentIllness;

    @Schema(description = "既往史")
    private String pastMedicalHistory;

    @Schema(description = "家族史")
    private String familyMedicalHistory;

    @Schema(description = "过敏史")
    private String allergyHistory;

    @Schema(description = "报告状态")
    @EnumValidate(message = "报告状态不正确",value = ReportEnum.class)
    private ReportEnum medicalRecordsStatus;

    @EnumConvert(value = ReportEnum.class,srcFieldName = "medicalRecordsStatus")
    @Schema(description = "报告状态名")
    private String reportStatusName;

    @Schema(description = "挂号日期")
    private LocalDate appointmentDate;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime appointmentStartTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime appointmentEndTime;

    @Schema(description = "就诊时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime visitingTime;

    @Schema(description = "图片信息")
    private List<FileMessage> fileList;
}
