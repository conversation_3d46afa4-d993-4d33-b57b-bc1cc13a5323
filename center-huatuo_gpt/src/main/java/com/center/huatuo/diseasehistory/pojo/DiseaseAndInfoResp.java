package com.center.huatuo.diseasehistory.pojo;

import com.center.framework.common.enumerate.DiseaseTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Map;

@Data
@AllArgsConstructor
public class DiseaseAndInfoResp extends UserInformation {

    @Schema(description = "疾病历史信息", example = "")
    private Map<DiseaseTypeEnum,DiseaseHistoryResp> diseaseMap;

    public DiseaseAndInfoResp() {
    }
}
