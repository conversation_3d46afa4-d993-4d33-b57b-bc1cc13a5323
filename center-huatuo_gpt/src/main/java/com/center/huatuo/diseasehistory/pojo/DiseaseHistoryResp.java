package com.center.huatuo.diseasehistory.pojo;

import com.center.framework.common.enumerate.DiseaseTypeEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DiseaseHistoryResp {

        @Schema(description = "疾病是否存在标示",example = "false")
        private Boolean isExist;

        @Schema(description = "疾病历史", example = "心脏病")
        private String diseaseHistory;

        @Schema(description = "疾病类型", example = "家族病史")
        private DiseaseTypeEnum diseaseType;

        @Schema(description = "疾病类型名称")
        @EnumConvert(value = DiseaseTypeEnum.class, srcFieldName = "diseaseType")
        private String diseaseTypeName;

        public DiseaseHistoryResp(Boolean isExist, String diseaseHistory, DiseaseTypeEnum diseaseType, String diseaseTypeName) {
                this.isExist = isExist;
                this.diseaseHistory = diseaseHistory;
                this.diseaseType = diseaseType;
                this.diseaseTypeName = diseaseTypeName;
        }
}
