package com.center.huatuo.diseasehistory.service;

import com.center.framework.common.enumerate.DiseaseTypeEnum;
import com.center.huatuo.diseasehistory.pojo.DiseaseAndInfoResp;
import com.center.huatuo.diseasehistory.pojo.DiseaseHistoryResp;
import com.center.huatuo.diseasehistory.pojo.DiseaseUpdateReq;
import com.center.huatuo.diseasehistory.pojo.UserInformationDTO;

import java.util.Map;

public interface DiseaseHistoryService {

  Map<DiseaseTypeEnum, DiseaseHistoryResp> getDisease(Long userId);

  void updateDisease(Map<DiseaseTypeEnum, DiseaseUpdateReq> diseaseUpdateReqMap);

  DiseaseAndInfoResp getDiseaseAndInfo(Long id);

  /**
   * 获取基本用户信息
   *
   * 此方法用于获取用户的基信息，包括用户名、用户ID等基本信息
   * 通过调用此方法，可以快速获取到用户的核心信息，以便在用户界面或其他需要的地方展示
   *
   * @return UserInformationDTO 返回一个包含用户基本信息的DTO对象
   */
  Map<String, Object> getBasicInfo();


}
