package com.center.huatuo.diseasehistory.service;

import com.center.cache.factory.CacheFactory;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.enumerate.DiseaseTypeEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.huatuo.diseasehistory.persistence.DiseaseHistoryModel;
import com.center.huatuo.diseasehistory.persistence.DiseaseHistoryRepository;
import com.center.huatuo.diseasehistory.pojo.*;
import com.center.infrastructure.system.biz.user.persistence.UserModel;
import com.center.infrastructure.system.biz.user.persistence.UserRepository;
import com.center.infrastructure.system.biz.user.pojo.UserInfo;
import com.center.infrastructure.system.biz.user.service.UserServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static java.lang.Character.toLowerCase;


@Service
@Slf4j
public class DiseaseHistoryServiceImpl implements DiseaseHistoryService {

    @Resource
    private DiseaseHistoryRepository diseaseHistoryRepository;

    @Resource
    private CacheFactory cacheFactory;
    @Resource
    private UserRepository userRepository;
    @Resource
    private UserServiceImpl userServiceImpl;


    @Override
    public Map<DiseaseTypeEnum, DiseaseHistoryResp> getDisease(Long userId) {
        // 1. 查询数据库，根据用户ID获取疾病历史记录
        List<DiseaseHistoryModel> diseaseHistoryList;
        try {
            diseaseHistoryList = diseaseHistoryRepository.findByUserId(userId);
        } catch (Exception e) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.GET_OBJECT_ERROR, e, "获取用户疾病信息列表失败");
        }

        // 2. 初始化疾病类型的默认数据
        Map<DiseaseTypeEnum, DiseaseHistoryResp> respMap = new HashMap<>();
        for (DiseaseTypeEnum typeEnum : DiseaseTypeEnum.values()) {
            respMap.put(typeEnum, new DiseaseHistoryResp(
                    false, // 默认不存在
                    null,  // 默认无疾病历史详情
                    typeEnum,
                    typeEnum.getDescription()
            ));
        }

        // 3. 如果有数据，根据数据库数据更新默认值
        if (diseaseHistoryList != null && !diseaseHistoryList.isEmpty()) {
            for (DiseaseHistoryModel diseaseHistory : diseaseHistoryList) {
                // 查找到对应的疾病类型，更新其值
                DiseaseHistoryResp resp = new DiseaseHistoryResp(
                        diseaseHistory.getIsExist(), // 存在数据
                        diseaseHistory.getDiseaseHistory(),
                        diseaseHistory.getDiseaseType(),
                        diseaseHistory.getDiseaseType().getDescription()
                );
                respMap.put(diseaseHistory.getDiseaseType(), resp);
            }
        }
        // 4. 返回结果
        return respMap;
    }

    @Override
    @Transactional
    public void updateDisease(Map<DiseaseTypeEnum, DiseaseUpdateReq> diseaseUpdateReqMap) {
        Long loginUserId = LoginContextHolder.getLoginUserId();

        //1.先获取数据库原来所有的数据
        List<DiseaseHistoryModel> diseaseHistoryModelList = diseaseHistoryRepository.findByUserId(loginUserId);

        //如果为空
        if (CollectionUtils.isEmpty(diseaseHistoryModelList)) {
            // 3. 遍历前端传入的数据，并根据是否存在的标志填充记录
            for (Map.Entry<DiseaseTypeEnum, DiseaseUpdateReq> entry : diseaseUpdateReqMap.entrySet()) {
                DiseaseTypeEnum diseaseType = entry.getKey(); // 获取疾病类型
                DiseaseUpdateReq updateReq = entry.getValue(); // 获取更新请求内容

                // 创建 DiseaseHistoryModel 对象并填充数据
                DiseaseHistoryModel diseaseHistory = new DiseaseHistoryModel();
                diseaseHistory.setUserId(loginUserId); // 设置用户ID
                diseaseHistory.setDiseaseType(diseaseType);      // 设置疾病类型
                diseaseHistory.setIsExist(updateReq.getIsExist());
                // 如果标示为 false，疾病历史描述为“”
                diseaseHistory.setDiseaseHistory(
                        Boolean.FALSE.equals(updateReq.getIsExist()) ? "" : updateReq.getDiseaseHistory()
                );

                // 添加到列表
                diseaseHistoryModelList.add(diseaseHistory);
            }
        } else {
            for (DiseaseHistoryModel list : diseaseHistoryModelList) {
                DiseaseUpdateReq updateReq = diseaseUpdateReqMap.get(list.getDiseaseType());
                if (updateReq != null) {
                    list.setIsExist(updateReq.getIsExist());
                    list.setDiseaseHistory(
                            Boolean.FALSE.equals(updateReq.getIsExist()) ? "" : updateReq.getDiseaseHistory()
                    );
                }
            }
        }

        // 4. 保存新数据到数据库
        diseaseHistoryRepository.saveAll(diseaseHistoryModelList);

    }

    /**
     * 根据用户ID用户疾病信息及登录信息，包括性别、年龄、出生日期等。
     *
     * @param id
     * @return 返回封装用户信息的 {@link DiseaseAndInfoResp} 对象
     * @throws ServiceExceptionUtil 如果用户名格式不正确（非中文格式），抛出参数不匹配异常
     */
    @Override
    public DiseaseAndInfoResp getDiseaseAndInfo(Long id) {
        DiseaseAndInfoResp diseaseAndInfoResp = OrikaUtils.convert(getUserInfo(id), DiseaseAndInfoResp.class);

        diseaseAndInfoResp.setDiseaseMap(getDisease(id));
        return diseaseAndInfoResp;
    }


    /**
     * 模型获取个人信息专用
     * 根据获取用户疾病信息及性别、年龄、姓名。
     *
     * @return 返回封装用户信息的 {@link UserInformationDTO} 对象
     */
    public Map<String, Object> getBasicInfo() {
        Long userId = LoginContextHolder.getLoginUserId();
        UserInformation info = getUserInfo(userId);

        // 创建返回的结果 Map
        Map<String, Object> result = new HashMap<>();
        result.put("name", info.getUsername());
        result.put("age", info.getAge().toString());

        // 判断性别并转换为 female/male
        String gender = "male"; // 默认设置为 male
        if ("女".equals(info.getGender())) {
            gender = "female";
        }
        result.put("gender", gender);

        // 查询用户的病史记录并展平
        List<DiseaseHistoryModel> historyModelList = diseaseHistoryRepository.findByUserId(userId);
        if (historyModelList == null || historyModelList.isEmpty()) {
            result.put("past_history", null);
            result.put("allergy_history", null);
            result.put("family_history", null);
        } else {
            historyModelList.forEach(historyModel -> {
                String key = historyModel.getDiseaseType().toString().toLowerCase();
                String value = historyModel.getDiseaseHistory();
                if (value != null) {
                    result.put(key, value); // 将病史记录展平到顶级字段
                }
            });
        }
        return result;
    }


    public UserInformation getUserInfo(Long userId) {
        UserInfo user = (UserInfo) cacheFactory.getHashCache().get(LoginContextHolder.getLoginUserId().toString());

        //如果缓存没信息则从数据库找
        if (user == null) {
            UserModel userModel = userRepository.findById(userId).orElseThrow(() ->
                    ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "此用户不存在"));
            user = OrikaUtils.convert(userModel, UserInfo.class);
        }
        String identityNumber = user.getIdentityNumber();

        UserInformation userInformation = new UserInformation();
        //1.设置用户信息
        userInformation.setUserId(user.getId());
        userInformation.setIdentityNumber(identityNumber);
        userInformation.setUserFirstName(user.getUserFirstName());
        userInformation.setUserLastName(user.getUserLastName());
        userInformation.setEmail(user.getEmail());
        userInformation.setPhoneNumber(user.getPhoneNumber());
        userInformation.setUserMaritalStatus(user.getUserMaritalStatus());
        userInformation.setUsername(userServiceImpl.getFullName(user.getUserLastName(), user.getUserFirstName()));
        userInformation.setDisplayName(userServiceImpl.getFullName(user.getUserLastName(), user.getUserFirstName()));

        // 2. 获取出生日期 (YYYY-MM-DD)
        String birthDateStr = identityNumber.substring(6, 14); // 提取出生日期部分
        LocalDate birthDate = LocalDate.parse(birthDateStr, DateTimeFormatter.BASIC_ISO_DATE);
        // 2.1 设置出生日期
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedBirthDate = birthDate.format(dateFormatter);
        userInformation.setBirthDate(formattedBirthDate);

        // 3. 获取性别 (第17位)
        char genderChar = identityNumber.charAt(16);
        String gender = (genderChar % 2 == 0) ? "女" : "男"; // 奇数为男，偶数为女
        userInformation.setGender(gender);

        // 4. 计算年龄
        LocalDate currentDate = LocalDate.now();
        int age = Period.between(birthDate, currentDate).getYears(); // 计算年龄
        userInformation.setAge((long) age);

        return userInformation;
    }

}
