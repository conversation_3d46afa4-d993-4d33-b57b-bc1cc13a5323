package com.center.huatuo.diseasehistory.pojo;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
@AllArgsConstructor
public class UserInformationDTO{

    @Schema(description = "名字")
    private String name;

    @Schema(description = "用户年龄")
    private Long age;

    @Schema(description = "用户性别")
    private String gender;

    private final Map<String, String> properties = new HashMap<>();

    public void addProperty(String key, String value) {
        if (value != null) {
            properties.put(key, value);
        }
    }

    @JsonAnyGetter
    public Map<String, String> getProperties() {
        return properties;
    }

    public UserInformationDTO() {
    }
}
