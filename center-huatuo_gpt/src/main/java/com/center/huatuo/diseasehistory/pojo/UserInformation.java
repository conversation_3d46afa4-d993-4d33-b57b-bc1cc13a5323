package com.center.huatuo.diseasehistory.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserInformation {
    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "名字")
    private String username;

    @Schema(description = "名字")
    private String DisplayName;

    @Schema(description = "手机号")
    private String phoneNumber;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "婚姻状态")
    private Boolean userMaritalStatus;

    @Schema(description = "用户性别")
    private String gender;

    @Schema(description = "用户年龄")
    private Long age;

    @Schema(description = "用户出生日期")
    private String  birthDate;

    @Schema(description = "身份证号")
    private String identityNumber;

    @Schema(description = "用户姓氏", example = "张")
    private String userLastName;

    @Schema(description = "用户名字", example = "三")
    private String userFirstName;
}
