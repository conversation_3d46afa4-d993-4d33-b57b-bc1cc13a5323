package com.center.huatuo.diseasehistory;

import com.center.framework.common.enumerate.DiseaseTypeEnum;
import com.center.framework.web.pojo.CommonResult;
import com.center.huatuo.diseasehistory.pojo.DiseaseAndInfoResp;
import com.center.huatuo.diseasehistory.pojo.DiseaseHistoryResp;
import com.center.huatuo.diseasehistory.pojo.DiseaseUpdateReq;
import com.center.huatuo.diseasehistory.service.DiseaseHistoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;


@Tag(name = "用户历史疾病")
@RestController
@RequestMapping("/disease")
@Validated
@Slf4j
public class DiseaseHistoryController {

    @Resource
    private DiseaseHistoryService diseaseHistoryService;

    @GetMapping("/get_disease_history")
    @Parameter(description = "用户ID")
    @Operation(summary = "根据用户ID查看历史疾病")
    public CommonResult<Map<DiseaseTypeEnum, DiseaseHistoryResp>> getDisease(@RequestParam(name = "userId") Long userId) {
        Map<DiseaseTypeEnum, DiseaseHistoryResp> result = diseaseHistoryService.getDisease(userId);
        return CommonResult.success(result);
    }

    @PostMapping("/update_disease_history")
    @Operation(summary = "更新用户疾病历史信息")
    @Parameter(description = "用户的疾病历史信息")
    public CommonResult<String> updateDiseaseHistory(@RequestBody @Valid Map<DiseaseTypeEnum, DiseaseUpdateReq> diseaseUpdateReqMap) {
        diseaseHistoryService.updateDisease(diseaseUpdateReqMap);
        return CommonResult.successWithMessageOnly("疾病历史更新成功");
    }

    @Operation(summary = "获取用户历史疾病及个人信息")
    @Parameter(description = "用户ID")
    @GetMapping(value = "/get_info/{id}")
    public CommonResult<DiseaseAndInfoResp> getLoginInfo(@PathVariable Long id) {
        return CommonResult.success(diseaseHistoryService.getDiseaseAndInfo(id));
    }

}
