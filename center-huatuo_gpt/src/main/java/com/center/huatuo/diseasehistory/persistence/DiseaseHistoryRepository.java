package com.center.huatuo.diseasehistory.persistence;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DiseaseHistoryRepository extends JpaRepository<DiseaseHistoryModel, Long>,
    QuerydslPredicateExecutor<DiseaseHistoryModel> {

    List<DiseaseHistoryModel> findByUserId(Long userId);

    void deleteByUserId(Long userId);
}
