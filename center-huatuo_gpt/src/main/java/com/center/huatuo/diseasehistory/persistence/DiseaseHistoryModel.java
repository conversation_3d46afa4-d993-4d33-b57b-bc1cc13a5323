package com.center.huatuo.diseasehistory.persistence;

import com.center.framework.common.enumerate.DiseaseTypeEnum;
import com.center.framework.db.core.BaseTenantModel;
import lombok.Data;

import javax.persistence.*;

@Table(name = "huatuo_user_disease_history")
@Entity
@Data
public class DiseaseHistoryModel extends BaseTenantModel {
    @Column(name = "user_id",nullable = false)
    private Long userId;

    @Column(name = "disease_history",nullable = false)
    private String diseaseHistory;

    @Column(name = "disease_type",nullable = false)
    @Enumerated(EnumType.STRING)
    private DiseaseTypeEnum diseaseType;

    @Column(name = "is_exist",nullable = false)
    private Boolean isExist;
}
