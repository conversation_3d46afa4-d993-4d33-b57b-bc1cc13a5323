package com.center.huatuo.diseasehistory.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;

@Data
public class DiseaseUpdateReq {

    @Schema(description = "疾病是否存在标示",example = "false")
    @NotNull(message = "疾病是否存在标示不能为空")
    private Boolean isExist;

    @Schema(description = "疾病历史", example = "心脏病")
    @NotNull(message = "疾病描述不能为空")
    private String diseaseHistory;
}
