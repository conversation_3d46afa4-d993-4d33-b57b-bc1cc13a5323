package com.center.huatuo.inquiry.review;


import com.center.framework.web.pojo.CommonResult;
import com.center.huatuo.inquiry.review.pojo.BaseHuatuoReview;
import com.center.huatuo.inquiry.review.pojo.HuatuoReviewResp;
import com.center.huatuo.inquiry.review.service.HuatuoReviewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@Tag(name = "问诊报告评价")
@RequestMapping("/inquiry/review")
@Validated
public class HuatuoReviewController {

    @Resource
    private HuatuoReviewService huatuoReviewService;

    @Operation(summary = "给问预诊报告进行评价")
    @PostMapping(value = "/post_review")
    public CommonResult<String> postReview(@RequestBody @Valid BaseHuatuoReview huatuoReview){
        huatuoReviewService.postReview(huatuoReview);
        return CommonResult.success();
    }

    @Operation(summary = "根据ID获取预问诊报告的评价内容")
    @GetMapping(value = "/get_review")
    public CommonResult<HuatuoReviewResp> getReview(@RequestParam Long id){
        return CommonResult.success(huatuoReviewService.getById(id));
    }
    @Operation(summary = "根据预问诊报告ID获取预问诊报告的评价内容")
    @GetMapping(value = "/get_review_with_medical_report_id")
    public CommonResult<HuatuoReviewResp> getReviewWithMedicalReportId(@RequestParam Long medicalReportId){
        return CommonResult.success(huatuoReviewService.getByMedicalReportId(medicalReportId));
    }
}
