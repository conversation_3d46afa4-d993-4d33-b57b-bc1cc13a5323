package com.center.huatuo.inquiry.appointment.service;

import cn.hutool.core.collection.CollUtil;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.huatuo.common.enumeration.HuatuoAppointmentDepartmentsNameEnum;
import com.center.huatuo.common.enumeration.HuatuoAppointmentDoctorLevelEnum;
import com.center.huatuo.common.enumeration.HuatuoAppointmentStatusEnum;
import com.center.huatuo.common.enumeration.HuatuoAppointmentsRegistrationTypeEnum;
import com.center.huatuo.inquiry.appointment.persistence.HuatuoAppointmentsModel;
import com.center.huatuo.inquiry.appointment.persistence.HuatuoAppointmentsRepository;
import com.center.huatuo.inquiry.appointment.persistence.QHuatuoAppointmentsModel;
import com.center.huatuo.inquiry.appointment.pojo.HuatuoAppointmentsResp;
import com.center.huatuo.inquiry.appointment.pojo.TimePeriod;
import com.center.huatuo.report.enumerate.ReportEnum;
import com.center.huatuo.report.persistence.QReportModel;
import com.center.huatuo.report.persistence.ReportModel;
import com.center.huatuo.report.persistence.ReportRepository;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import static com.querydsl.core.types.dsl.Expressions.stringTemplate;


@Service
@Slf4j
public class HuatuoAppointmentsServiceImpl implements HuatuoAppointmentsService {

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    @Resource
    private HuatuoAppointmentsRepository huatuoAppointmentsRepository;

    @Resource
    private ReportRepository reportRepository;

    @Resource
    private JPAQueryFactory queryFactory;

    /**
     * 根据用户ID列出未就诊的挂号记录
     * 此方法首先尝试从数据库中获取所有未完成的预约如果不存在未就真的挂号记录，则自动生成三个挂号记录并返回
     *
     * @param userId 用户ID，用于查询挂号记录信息
     * @return 返回一个包含未就诊的挂号记录的列表
     */
    @Override
    public List<HuatuoAppointmentsResp> listUncompletedAppointment(Long userId) {
        // 查询数据库中所有未就诊的挂号记录
        QHuatuoAppointmentsModel qHuatuoAppointmentsModel = QHuatuoAppointmentsModel.huatuoAppointmentsModel;
        QReportModel qReportModel = QReportModel.reportModel;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qHuatuoAppointmentsModel.userId.eq(userId))
                .and(qHuatuoAppointmentsModel.status.eq(HuatuoAppointmentStatusEnum.UNATTENDED));
        List<HuatuoAppointmentsResp> result = queryFactory.select((Projections.bean(
                        HuatuoAppointmentsResp.class,
                        qHuatuoAppointmentsModel.id,
                        qHuatuoAppointmentsModel.registrationType,
                        qHuatuoAppointmentsModel.doctorName,
                        qHuatuoAppointmentsModel.doctorLevel,
                        qHuatuoAppointmentsModel.departmentsName,
                        qHuatuoAppointmentsModel.appointmentDate,
                        stringTemplate("CONCAT(DATE_FORMAT({0}, '%H:%i'), '~', DATE_FORMAT({1}, '%H:%i'))",
                                qHuatuoAppointmentsModel.appointmentStartTime,
                                qHuatuoAppointmentsModel.appointmentEndTime).as("appointmentTime"),
                        qHuatuoAppointmentsModel.latestSessionId,
                        qHuatuoAppointmentsModel.latestMedicalRecordsId,
                        qHuatuoAppointmentsModel.status,
                        qReportModel.medicalRecordsStatus.as("latestMedicalRecordsStatus"),
                        qReportModel.medicalRecordsSessionId.as("latestMedicalRecordsSessionId")
                )))
                .from(qHuatuoAppointmentsModel)
                .leftJoin(qReportModel).on(qHuatuoAppointmentsModel.latestMedicalRecordsId.eq(qReportModel.id))
                .where(builder)
                .orderBy(qHuatuoAppointmentsModel.appointmentDate.asc(), qHuatuoAppointmentsModel.appointmentStartTime.asc())
                .fetch();
        if (!result.isEmpty()) {
            return result;
        } else {
            // 如果不存在未就诊的挂号记录，自动生成三个挂号记录
            int i;
            result = CollUtil.newArrayList();
            for (i = 0; i < 5; i++) {
                HuatuoAppointmentsModel huatuoAppointmentsModel = generateHuatuoAppointment(userId);
                HuatuoAppointmentsResp huatuoAppointmentsResp = convert2HuatuoAppointmentsResp(huatuoAppointmentsModel);
                result.add(huatuoAppointmentsResp);
            }
            return result;
        }
    }

    @Override
    @Scheduled(fixedRate = 1000 * 60 * 5)
    @Async("scheduledTaskExecutor")
    @Transactional
    public void updateStatus() {
        log.info("开始更新挂号记录状态");
        List<HuatuoAppointmentsModel> huatuoAppointmentsModels = huatuoAppointmentsRepository.findAllByStatus(HuatuoAppointmentStatusEnum.UNATTENDED);
        for (HuatuoAppointmentsModel huatuoAppointmentsModel : huatuoAppointmentsModels) {
            LocalDate appointmentDate = huatuoAppointmentsModel.getAppointmentDate();
            LocalTime appointmentEndTime = huatuoAppointmentsModel.getAppointmentEndTime();
            if (appointmentDate.isBefore(LocalDate.now())) {
                huatuoAppointmentsModel.setStatus(HuatuoAppointmentStatusEnum.ATTENDED);
                huatuoAppointmentsModel.setVisitingTime(LocalDateTime.now());
                huatuoAppointmentsRepository.save(huatuoAppointmentsModel);
            } else if (appointmentDate.equals(LocalDate.now())) {
                if (appointmentEndTime.isBefore(LocalTime.now())) {
                    huatuoAppointmentsModel.setStatus(HuatuoAppointmentStatusEnum.ATTENDED);
                    huatuoAppointmentsModel.setVisitingTime(LocalDateTime.now());
                    huatuoAppointmentsRepository.save(huatuoAppointmentsModel);
                }
            }
        }
        log.info("更新挂号记录状态完毕");
    }

    @Override
    public void generateAppointments() {
        int i;
        List<HuatuoAppointmentsResp> result = CollUtil.newArrayList();
        for (i = 0; i < 5; i++) {
            HuatuoAppointmentsModel huatuoAppointmentsModel = generateHuatuoAppointment(LoginContextHolder.getLoginUserId());
            HuatuoAppointmentsResp huatuoAppointmentsResp = convert2HuatuoAppointmentsResp(huatuoAppointmentsModel);
            result.add(huatuoAppointmentsResp);
        }
    }

    @Override
    public HuatuoAppointmentsResp getAppointment(Long id) {
        HuatuoAppointmentsModel huatuoAppointmentsModel = huatuoAppointmentsRepository.findById(id).orElseThrow(
                () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "挂号记录不存在")
        );
        HuatuoAppointmentsResp huatuoAppointmentsResp = convert2HuatuoAppointmentsResp(huatuoAppointmentsModel);
        if (huatuoAppointmentsModel.getLatestMedicalRecordsId()!=null){
            ReportModel reportModel = reportRepository.findById(huatuoAppointmentsModel.getLatestMedicalRecordsId()).orElseThrow(
                    () -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "问诊报告不存在")
            );
            huatuoAppointmentsResp.setLatestMedicalRecordsStatus(reportModel.getMedicalRecordsStatus());
            huatuoAppointmentsResp.setLatestMedicalRecordsSessionId(reportModel.getMedicalRecordsSessionId());
        }
        return huatuoAppointmentsResp;
    }

    public HuatuoAppointmentsResp convert2HuatuoAppointmentsResp(HuatuoAppointmentsModel huatuoAppointmentsModel) {
        HuatuoAppointmentsResp huatuoAppointmentsResp = OrikaUtils.convert(huatuoAppointmentsModel, HuatuoAppointmentsResp.class);
        huatuoAppointmentsResp.setAppointmentTime(getAppointmentTime(huatuoAppointmentsModel));
        return huatuoAppointmentsResp;
    }

    private HuatuoAppointmentsModel generateHuatuoAppointment(Long userId) {
        //随机选择科室名称
        HuatuoAppointmentDepartmentsNameEnum departmentsName = HuatuoAppointmentDepartmentsNameEnum.getRandom();
        //随机选择号别
        HuatuoAppointmentsRegistrationTypeEnum registrationType = HuatuoAppointmentsRegistrationTypeEnum.getRandom();
        //设置医生名称和医生级别
        String doctorName = "华佗";
        String doctorLevel = HuatuoAppointmentDoctorLevelEnum.PHYSICIAN.getDescription();
        if (registrationType.equals(HuatuoAppointmentsRegistrationTypeEnum.EXPERT)) {
            doctorName = "华佗";
            doctorLevel = HuatuoAppointmentDoctorLevelEnum.getRandom().getDescription();
        }
        //随机选择预约时间
        LocalDate appointmentDate = getRandomDateWithinNextWeek();
        //检查日期是否已预约同一科室
        int attempts = 0;
        while (!checkAppointmentDate(appointmentDate, departmentsName)) {
            departmentsName = HuatuoAppointmentDepartmentsNameEnum.getRandom();
            appointmentDate = getRandomDateWithinNextWeek();
            attempts++;
            if (attempts == 10) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OVER_LIMIT, "尝试生成次数过多，请稍后重试");
            }
        }
        //随机选择预约时间段
        TimePeriod randomTimePeriod = getRandomTimePeriod();
        LocalTime appointmentStartTime = randomTimePeriod.getStartTime();
        LocalTime appointmentEndTime = randomTimePeriod.getEndTime();
        //生成挂号记录
        HuatuoAppointmentsModel huatuoAppointmentsModel = new HuatuoAppointmentsModel();
        huatuoAppointmentsModel.setUserId(userId);
        huatuoAppointmentsModel.setHospitalDepartmentId(1L);
        huatuoAppointmentsModel.setDepartmentsName(departmentsName.getDescription());
        huatuoAppointmentsModel.setDoctorName(doctorName);
        huatuoAppointmentsModel.setDoctorLevel(doctorLevel);
        huatuoAppointmentsModel.setRegistrationType(registrationType.getDescription());
        huatuoAppointmentsModel.setAppointmentDate(appointmentDate);
        huatuoAppointmentsModel.setAppointmentStartTime(appointmentStartTime);
        huatuoAppointmentsModel.setAppointmentEndTime(appointmentEndTime);
        huatuoAppointmentsModel.setStatus(HuatuoAppointmentStatusEnum.UNATTENDED);
        huatuoAppointmentsRepository.save(huatuoAppointmentsModel);
        return huatuoAppointmentsModel;


    }

    private Boolean checkAppointmentStartTime(HuatuoAppointmentDepartmentsNameEnum departmentsName, LocalDate appointmentDate, LocalTime appointmentStartTime) {
        List<HuatuoAppointmentsModel> huatuoAppointmentsModels = huatuoAppointmentsRepository.findAllByUserIdAndDepartmentsNameAndAppointmentDateAndAppointmentEndTime(LoginContextHolder.getLoginUserId(), departmentsName.getDescription(), appointmentDate, appointmentStartTime);
        return huatuoAppointmentsModels.isEmpty();
    }

    public TimePeriod getRandomTimePeriod() {
        Random random = new Random();
        List<TimePeriod> timePeriods = new ArrayList<>();
        timePeriods.add(new TimePeriod(LocalTime.of(8, 0), LocalTime.of(9, 0)));
        timePeriods.add(new TimePeriod(LocalTime.of(9, 0), LocalTime.of(10, 0)));
        timePeriods.add(new TimePeriod(LocalTime.of(10, 0), LocalTime.of(11, 0)));
        timePeriods.add(new TimePeriod(LocalTime.of(13, 30), LocalTime.of(14, 30)));
        timePeriods.add(new TimePeriod(LocalTime.of(14, 30), LocalTime.of(15, 30)));
        timePeriods.add(new TimePeriod(LocalTime.of(15, 30), LocalTime.of(16, 30)));
        TimePeriod selectedPeriod = timePeriods.get(random.nextInt(timePeriods.size()));
        return selectedPeriod;
    }

    private Boolean checkAppointmentDate(LocalDate appointmentDate, HuatuoAppointmentDepartmentsNameEnum departmentsName) {
        List<HuatuoAppointmentsModel> huatuoAppointmentsModels = huatuoAppointmentsRepository.findAllByUserIdAndDepartmentsNameAndAppointmentDate(LoginContextHolder.getLoginUserId(), departmentsName.getDescription(), appointmentDate);
        return huatuoAppointmentsModels.isEmpty();
    }


    public LocalDate getRandomDateWithinNextWeek() {
        LocalDate today = LocalDate.now();
        Random random = new Random();
        int daysToAdd = random.nextInt(7) + 1;
        LocalDate randomFutureDate = today.plusDays(daysToAdd);
        return randomFutureDate;
    }

    private String getAppointmentTime(HuatuoAppointmentsModel unattendedAppointment) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        String startTime = unattendedAppointment.getAppointmentStartTime().format(dateTimeFormatter);
        String endTime = unattendedAppointment.getAppointmentEndTime().format(dateTimeFormatter);
        String appointmentTime = startTime + "~" + endTime;
        return appointmentTime;
    }
}
