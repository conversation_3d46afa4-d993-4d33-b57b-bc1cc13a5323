package com.center.huatuo.inquiry.review.persistence;

import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Entity
@Table(name = "huatuo_reviews")
@Data
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class HuatuoReviewModel extends BaseTenantModel {

    @Column(name = "medical_records_id",  nullable = false)
    private Long medicalRecordsId;

    @Column(name = "rating",  nullable = false)
    private Integer rating;

    @Column(name = "review_time")
    private LocalDateTime reviewTime;

    @Column(name = "feedback")
    private String feedback;

}
