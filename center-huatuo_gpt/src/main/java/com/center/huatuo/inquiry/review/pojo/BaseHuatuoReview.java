package com.center.huatuo.inquiry.review.pojo;

import javax.validation.constraints.*;
import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class BaseHuatuoReview {

    @NotNull(message = "问诊报告ID不能为空")
    @Schema(description = "问诊报告ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456789")
    private Long medicalRecordsId;

    @NotNull(message = "评分不能为空")
    @Schema(description = "评分（1-5星）", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
    @Min(value = 1, message = "评分必须大于等于1")
    @Max(value = 5, message = "评分必须小于等于5")
    private Integer rating;

    @Schema(description = "患者的反馈", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "医生很专业，服务态度好")
    @Size(max = 65535, message = "反馈内容长度不能超过65535个字符") // TEXT类型在MySQL中最大长度为65535字节
    private String feedback;
}
