package com.center.huatuo.inquiry.appointment.service;

import com.center.huatuo.inquiry.appointment.pojo.HuatuoAppointmentsResp;

import java.util.List;

public interface HuatuoAppointmentsService {


    /**
     * 根据用户ID查询些用户已预约未就诊的记录
     * @param userId
     * @return
     */
    List<HuatuoAppointmentsResp> listUncompletedAppointment(Long userId);

    void updateStatus();

    void generateAppointments();

    HuatuoAppointmentsResp getAppointment(Long id);
}
