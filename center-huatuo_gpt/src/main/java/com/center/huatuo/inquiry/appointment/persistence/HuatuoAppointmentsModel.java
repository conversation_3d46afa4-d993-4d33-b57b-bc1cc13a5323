package com.center.huatuo.inquiry.appointment.persistence;

import com.center.framework.db.annotation.NotIgnoreNullField;
import com.center.framework.db.core.BaseTenantModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import com.center.huatuo.common.enumeration.HuatuoAppointmentStatusEnum;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;

@Data
@Table(name = "huatuo_appointments")
@Entity
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
@NotIgnoreNullField
public class HuatuoAppointmentsModel extends BaseTenantModel {

        @Column(name = "user_id", nullable = false)
        private Long userId;

        @Column(name = "hospital_department_id", nullable = false)
        private Long hospitalDepartmentId;

        @Column(name = "departments_name", length = 255)
        private String departmentsName;

        @Column(name = "doctor_id")
        private Long doctorId;

        @Column(name = "doctor_name", length = 255)
        private String doctorName;

        @Column(name = "registration_type", length = 50)
        private String registrationType;

        @Column(name = "doctor_level", length = 255)
        private String doctorLevel;

        @Column(name = "appointment_date", nullable = false)
        private LocalDate appointmentDate;

        @Column(name = "appointment_start_time", nullable = false)
        private LocalTime appointmentStartTime;

        @Column(name = "appointment_end_time", nullable = false)
        private LocalTime appointmentEndTime;

        @Column(name = "visiting_time")
        private LocalDateTime visitingTime;

        @Column(name = "status", length = 255)
        @Enumerated(value = EnumType.STRING)
        private HuatuoAppointmentStatusEnum status;

        @Column(name = "latest_session_id")
        private Long latestSessionId;

        @Column(name = "latest_medical_records_id")
        private Long latestMedicalRecordsId;
}
