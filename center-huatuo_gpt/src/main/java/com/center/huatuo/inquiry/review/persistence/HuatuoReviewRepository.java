package com.center.huatuo.inquiry.review.persistence;

import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface HuatuoReviewRepository extends JoinFetchCapableQueryDslJpaRepository<HuatuoReviewModel,Long> {

    boolean existsByMedicalRecordsId(Long medicalRecordId);

    HuatuoReviewModel getByMedicalRecordsId(Long medicalRecordId);
}
