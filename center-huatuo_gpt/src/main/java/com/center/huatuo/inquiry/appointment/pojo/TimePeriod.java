package com.center.huatuo.inquiry.appointment.pojo;

import lombok.Data;

import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/12/6 13:33
 */
@Data
public class TimePeriod {
    private final LocalTime startTime;
    private final LocalTime endTime;

    public TimePeriod(LocalTime startTime, LocalTime endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
    }

    public LocalTime getStartTime() {
        return startTime;
    }

    public LocalTime getEndTime() {
        return endTime;
    }
}
