package com.center.huatuo.inquiry.appointment.persistence;

import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;
import com.center.huatuo.common.enumeration.HuatuoAppointmentStatusEnum;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

@Repository
public interface HuatuoAppointmentsRepository extends JoinFetchCapableQueryDslJpaRepository<HuatuoAppointmentsModel, Long> {

    List<HuatuoAppointmentsModel> findAllByUserIdAndStatus(Long userId, HuatuoAppointmentStatusEnum status);

    List<HuatuoAppointmentsModel> findAllByUserIdAndDepartmentsNameAndAppointmentDate(Long loginUserId, String departmentsName, LocalDate appointmentDate);

    List<HuatuoAppointmentsModel> findAllByUserIdAndDepartmentsNameAndAppointmentDateAndAppointmentEndTime(Long loginUserId, String departmentsName, LocalDate appointmentDate, LocalTime appointmentEndTime);

    List<HuatuoAppointmentsModel> findAllByStatus(HuatuoAppointmentStatusEnum status);

}
