package com.center.huatuo.inquiry.appointment;


import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.pojo.CommonResult;
import com.center.huatuo.inquiry.appointment.pojo.HuatuoAppointmentsResp;
import com.center.huatuo.inquiry.appointment.service.HuatuoAppointmentsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Tag(name = "挂号记录")
@RequestMapping("/appointment")
@Validated
public class HuatuoAppointmentController {

    @Resource
    private HuatuoAppointmentsService huatuoAppointmentsService;

    @EnumConvertPoint
    @GetMapping("/list_uncompleted")
    @Operation(summary = "查询出用户未完成的挂号记录")
    public CommonResult<List<HuatuoAppointmentsResp>> listUncompleted() {
        return CommonResult.success(huatuoAppointmentsService.listUncompletedAppointment(LoginContextHolder.getLoginUserId()));
    }

    @PostMapping("/update_status")
    @Operation(summary = "更新挂号记录状态")
    public CommonResult<String> updateStatus() {
    	huatuoAppointmentsService.updateStatus();
    	return CommonResult.successWithMessageOnly("更新挂号记录状态成功");
    }

    @PostMapping("/generate_appointments")
    @Operation(summary = "（临时接口，仅测试用)手动生成5条挂号记录")
    public CommonResult<String> generateAppointments() {
    	huatuoAppointmentsService.generateAppointments();
    	return CommonResult.successWithMessageOnly("手动生成挂号记录成功");
    }

    @EnumConvertPoint
    @GetMapping("/get_appointment")
    @Operation(summary = "查询挂号记录详情")
    @Parameter(description = "挂号记录ID")
    public CommonResult<HuatuoAppointmentsResp> getAppointment(@RequestParam Long id) {
        return CommonResult.success(huatuoAppointmentsService.getAppointment(id));
    }
}
