package com.center.huatuo.inquiry.appointment.pojo;

import com.center.framework.common.annotation.enumvalidate.EnumValidate;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.center.huatuo.common.enumeration.HuatuoAppointmentDoctorLevelEnum;
import com.center.huatuo.common.enumeration.HuatuoAppointmentStatusEnum;
import com.center.huatuo.report.enumerate.ReportEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

@Data
public class HuatuoAppointmentsResp {

    @Schema(name = "id", description = "挂号记录ID")
    private Long id;

    @Schema(name = "registrationType", description = "挂号类型")
    private String registrationType;

    @Schema(name = "doctorName", description = "医生名称")
    private String doctorName;

    @Schema(name = "doctorLevel", description = "医生级别")
    private String doctorLevel;

    @Schema(name = "departmentsName", description = "科室名称")
    private String departmentsName;

    @Schema(name = "appointmentDate",description = "预约就诊日期",example = "2024-05-01")
    private LocalDate appointmentDate;

    @Schema(name = "appointmentTime", description = "预约就诊时间段", example = "10:30~11:00")
    private String appointmentTime;

    @Schema(name = "latestSessionId", description = "最新一次问诊对话SessionID", example = "12345")
    private Long latestSessionId;

    @Schema(name = "latestMedicalRecordsId",description = "最新问诊报告Id")
    private Long latestMedicalRecordsId;

    @Schema(name = "status", description = "挂号状态，未就诊、已就诊、取消")
    private HuatuoAppointmentStatusEnum status;

    @EnumConvert(value = HuatuoAppointmentStatusEnum.class,srcFieldName = "status")
    @Schema(name = "statusName",description = "挂号状态名")
    private String statusName;

    @Schema(name = "latestMedicalRecordsStatus",description = "最新问诊报告状态")
    private ReportEnum latestMedicalRecordsStatus;

    @EnumConvert(value = ReportEnum.class,srcFieldName = "latestMedicalRecordsStatus")
    @Schema(name = "latestMedicalRecordsStatusName",description = "最新问诊报告状态名")
    private String latestMedicalRecordsStatusName;

    @Schema(name = "latestMedicalRecordsSessionId",description = "最新问诊报告会话Id")
    private Long latestMedicalRecordsSessionId;

}
