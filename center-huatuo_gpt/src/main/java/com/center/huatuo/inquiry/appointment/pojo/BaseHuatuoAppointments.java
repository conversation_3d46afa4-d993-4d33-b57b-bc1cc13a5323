package com.center.huatuo.inquiry.appointment.pojo;

import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.center.huatuo.common.enumeration.HuatuoAppointmentStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class BaseHuatuoAppointments {

    @NotNull(message = "ID不能为空")
    @Schema(description = "挂号记录唯一标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456789")
    private Long userId;

    @NotNull(message = "挂号科室ID不能为空")
    @Schema(description = "挂号科室id", requiredMode = Schema.RequiredMode.REQUIRED, example = "987654321")
    private Long hospitalDepartmentId;

    @NotEmpty(message = "用户ID不能为空")
    @Schema(description = "科室名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "内科")
    @Size(max = 100, message = "科室名称长度不能超过255个字符")
    private String departmentsName;

    @Schema(description = "医生id", requiredMode = Schema.RequiredMode.REQUIRED, example = "555555555")
    private Long doctorId;

    @NotEmpty(message = "医生姓名不能为空")
    @Schema(description = "医生姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @Size(max = 100, message = "医生姓名长度不能超过255个字符")
    private String doctorName;

    @NotEmpty(message = "挂号类型不能为空")
    @Schema(description = "挂号类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "普通挂号")
    @Size(max = 20, message = "挂号类型长度不能超过50个字符")
    private String registrationType;

    @NotEmpty(message = "医生级别不能为空")
    @Schema(description = "医生级别（例如：主任医师、副主任医师、医师等）", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "主任医师")
    @Size(max = 100, message = "医生级别长度不能超过255个字符")
    private String doctorLevel;

    @NotNull(message = "预约日期不能为空")
    @Schema(description = "预约日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-10-01")
    @PastOrPresent(message = "预约日期必须是过去或现在的日期")
    private Date appointmentDate;

    @NotNull(message = "预约开始时间不能为空")
    @Schema(description = "预约开始时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "08:00:00")
    private LocalDateTime appointmentStartTime;

    @NotNull(message = "预约结束时间不能为空")
    @Schema(description = "预约结束时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "09:00:00")
    @FutureOrPresent(message = "预约结束时间必须是现在或未来的时间")
    private LocalDateTime appointmentEndTime;

    @Schema(description = "就诊时间", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "2023-10-01 08:30:00")
    private LocalDateTime visitingTime;

    @Schema(description = "挂号状态，未就诊、已就诊、取消", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "UNATTENDED")
    private HuatuoAppointmentStatusEnum status;

    @EnumConvert(value = HuatuoAppointmentStatusEnum.class,srcFieldName = "status")
    @Schema(description = "挂号状态名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "未就诊")
    private String statusName;


    @Schema(description = "最新会话id", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "666666666")
    private Long latestSessionId;

    @Schema(description = "最新问诊报告id", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "666666666")
    private Long latestMedicalRecordsId;
}
