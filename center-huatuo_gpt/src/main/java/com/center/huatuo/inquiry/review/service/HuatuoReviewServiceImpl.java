package com.center.huatuo.inquiry.review.service;

import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.huatuo.inquiry.review.persistence.HuatuoReviewModel;
import com.center.huatuo.inquiry.review.persistence.HuatuoReviewRepository;
import com.center.huatuo.inquiry.review.pojo.BaseHuatuoReview;
import com.center.huatuo.inquiry.review.pojo.HuatuoReviewResp;
import com.center.huatuo.report.enumerate.ReportEnum;
import com.center.huatuo.report.persistence.ReportModel;
import com.center.huatuo.report.persistence.ReportRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.Optional;

@Service
@Slf4j
public class HuatuoReviewServiceImpl implements HuatuoReviewService {

  @Resource private HuatuoReviewRepository huatuoReviewRepository;

  @Resource private ReportRepository reportRepository;

  @Override
  @Transactional
  public void postReview(BaseHuatuoReview huatuoReviewReq) {
    if (!reportRepository.findById(huatuoReviewReq.getMedicalRecordsId()).isPresent()) {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "预问诊报告不存在！");
    }
    ReportModel reportModel = reportRepository.findById(huatuoReviewReq.getMedicalRecordsId()).get();
    if (huatuoReviewRepository.existsByMedicalRecordsId(huatuoReviewReq.getMedicalRecordsId())) {
      throw ServiceExceptionUtil.exception(
          GlobalErrorCodeConstants.DUPLICATED_OBJECT, "此问诊报告已经评价过，不能重复评价！");
    }
    HuatuoReviewModel huatuoReviewModel =
        OrikaUtils.convert(huatuoReviewReq, HuatuoReviewModel.class);
    huatuoReviewModel.setReviewTime(LocalDateTime.now());
    huatuoReviewRepository.save(huatuoReviewModel);
    reportModel.setMedicalRecordsStatus(ReportEnum.EVALUATED);
    reportRepository.save(reportModel);
  }

  @Override
  public HuatuoReviewResp getById(Long id) {
    Optional<HuatuoReviewModel> optional = huatuoReviewRepository.findById(id);
    HuatuoReviewResp huatuoReview = null;
    if (optional.isPresent()) {
      huatuoReview = OrikaUtils.convert(optional.get(), HuatuoReviewResp.class);
    } else {
      throw ServiceExceptionUtil.exception(
          GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "预问诊报告的评价不存在");
    }
    return huatuoReview;
  }

  @Override
  public HuatuoReviewResp getByMedicalReportId(Long medicalReportId) {

    if (!reportRepository.findById(medicalReportId).isPresent()) {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "预问诊报告不存在！");
    }
    HuatuoReviewModel huatuoReviewModel = huatuoReviewRepository.getByMedicalRecordsId(medicalReportId);
    HuatuoReviewResp huatuoReview = null;
    if (huatuoReviewModel ==  null || huatuoReviewModel.getId() == null) {
      throw ServiceExceptionUtil.exception(
              GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "预问诊报告的评价不存在");
    } else {
      huatuoReview = OrikaUtils.convert(huatuoReviewModel, HuatuoReviewResp.class);
    }
    return huatuoReview;
  }
}
