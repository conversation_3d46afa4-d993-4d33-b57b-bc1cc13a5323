package com.center.huatuo.inquiry.review.service;

import com.center.huatuo.inquiry.review.pojo.BaseHuatuoReview;
import com.center.huatuo.inquiry.review.pojo.HuatuoReviewResp;

public interface HuatuoReviewService {

    /**
     * 用户对系统生成的问诊报告内容进行评价
     * @param huatuoReview
     */
    void postReview(BaseHuatuoReview huatuoReview);


    /**
     * 根据ID获取评价详情
     * @param id
     * @return
     */
    HuatuoReviewResp getById(Long id);

    /**
     * 根据预问诊报告查询对应的评价内容
     * @param medicalReportId 问诊报告ID
     * @return
     */
    HuatuoReviewResp getByMedicalReportId(Long medicalReportId);
}
