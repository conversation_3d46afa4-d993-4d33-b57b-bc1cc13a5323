package com.center.huatuo.chat.consultation.persistence;

import com.center.framework.db.annotation.GenerateId;
import com.center.framework.db.core.BaseModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

/**
 * @Date: 2023/3/13 20:12
 * @Description:
 */
@Data
@Table(name = "huatuo_consultation_chat_session")
@Entity
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class ConsultationChatSessionModel extends BaseModel {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 挂号id
     */
    private Long appointmentId;
    /**
     * 结束时间
     */
    private Date etime;
    /**
     * 给评分
     */
    private Integer score;
    /**
     * 调用模型名称
     */
    private String modelName;

}
