package com.center.huatuo.chat.registration.persistence;

import com.center.framework.db.annotation.GenerateId;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Date: 2023/3/13 20:12
 * @Description:
 */
@Data
@Table(name = "chat_session")
@Entity
public class ChatSessionModel {

    @Id
    @Column(name = "id", nullable = false, updatable = false)
    @GenerateId
   private Long id;
    /**
     * 创建时间
     */
    private Date ctime;
    /**
     * 结束时间
     */
    private Date etime;
    /**
     * 给评分
     */
    private Integer score;
    /**
     * 调用模型名称
     */
    private String modelName;

}
