package com.center.huatuo.chat.consultation.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "模拟生成报告")
public class MockReportReq {
    @NotNull(message = "挂号id不能为空")
    @Schema(description = "挂号id")
    private Long appointmentId;

    @Schema(description = "问题出现的时间")
    private String time;

    @Schema(description = "疼痛部位")
    private String painPosition;

    @Schema(description = "疼痛类型")
    private String painType;

    @Schema(description = "疼痛持续性")
    private String painDuration;

    @Schema(description = "疾病史")
    private String pastMedicalHistory;

    @Schema(description = "过敏史")
    private String allergyHistory;

    @Schema(description = "家族病史")
    private String familyMedicalHistory;
}
