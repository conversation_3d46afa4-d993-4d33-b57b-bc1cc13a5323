package com.center.huatuo.chat.registration.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class ChatReq {

    @NotEmpty(message = "问题不能为空")
    @Schema(description = "问题内容")
    private String question;

    @Schema(description = "会话ID")
    @NotNull(message = "会话ID不能为空")
    private Long sessionId;

    @NotEmpty(message = "用户基本信息不能为空")
    @Schema(description = "用户基本信息")
    private String profilePrompt;

    @Schema(description = "第三方标识")
    private String thirdPartyIdentification;

}
