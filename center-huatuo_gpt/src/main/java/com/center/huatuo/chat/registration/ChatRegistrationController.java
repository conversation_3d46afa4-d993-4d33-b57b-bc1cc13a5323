package com.center.huatuo.chat.registration;


import cn.hutool.core.lang.Snowflake;

import com.center.huatuo.chat.registration.pojo.ChatReq;
import com.center.huatuo.chat.registration.service.ChatService;
import com.center.framework.web.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@Tag(name = "智能导诊")
@RequestMapping("/chat_registration")
@Validated
@Slf4j
public class ChatRegistrationController {

    @Resource
    Snowflake snowflake;

    @Resource
    private ChatService chatService;

    @GetMapping("/init_session")
    @Operation(summary = "获取对话初始化sessionID")
    public CommonResult<Long> iniSession(){
        return CommonResult.success(snowflake.nextId());
    }

    @PostMapping(value = "/registration_chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "智能导诊对话")
    public SseEmitter chatWithRobot(@RequestBody @Valid ChatReq chatReq){
        return chatService.chat(chatReq);
    }



    @PostMapping(value = "/thumbs_up/{id}")
    @Operation(summary = "给回答的内容点赞")
    public CommonResult<String> thumbsUp(@PathVariable(name = "id") Long id){
        chatService.thumbsUp(id);
        return CommonResult.success();
    }

    @PostMapping(value = "/thumbs_down/{id}")
    @Operation(summary = "给回答的内容点倒赞")
    public CommonResult<String> thumbsDown(@PathVariable(name = "id") Long id){
        chatService.thumbsDown(id);
        return CommonResult.success();
    }

    @PostMapping(value = "/thumbs_cancel/{id}")
    @Operation(summary = "取消给回答的内容点赞或倒赞/")
    public CommonResult<String> thumbsCancel(@PathVariable(name = "id") Long id){
        chatService.thumbsCancel(id);
        return CommonResult.success();
    }
}
