package com.center.huatuo.chat.consultation.service;


import cn.hutool.core.lang.Snowflake;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.center.cache.factory.CacheFactory;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.CommonResult;
import com.center.huatuo.chat.consultation.persistence.*;
import com.center.huatuo.chat.consultation.pojo.*;
import com.center.huatuo.common.utils.AntispamUtil;
import com.center.huatuo.common.utils.OkHttpUtils;
import com.center.huatuo.diseasehistory.service.DiseaseHistoryService;
import com.center.huatuo.inquiry.appointment.persistence.HuatuoAppointmentsModel;

import com.center.huatuo.inquiry.appointment.persistence.HuatuoAppointmentsRepository;
import com.center.huatuo.report.enumerate.ReportEnum;
import com.center.huatuo.report.persistence.*;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSources;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION;
import static com.center.huatuo.chat.consultation.cacheHelper.CacheHelper.getCacheCategories;

@Slf4j
@Service
public class ConsultationChatServiceImpl implements ConsultationChatService {


    @Resource
    private HuatuoAppointmentsRepository appointmentsRepository;

    @Resource
    private Snowflake snowflake;

    @Resource
    AntispamUtil antispamUtil;

    @Autowired
    @Qualifier("seeExecutor")
    private Executor seeExecutor;

    @Value("${huatuo-gpt.consult_url}")
    private String modelUrl;

    @Value("${huatuo-gpt.consult_report_url}")
    private String modelReportUrl;

    @Resource
    private JPAQueryFactory queryFactory;

    @Resource
    private ConsultationChatQuestionRepository consultationChatQuestionRepository;

    @Resource
    private ConsultationChatAnswerRepository consultationChatAnswerRepository;

    @Resource
    private ConsultationChatSessionRepository consultationChatSessionRepository;
    @Resource
    private ReportRepository reportRepository;
    @Resource
    private CacheFactory cacheFactory;

    @Resource
    private DiseaseHistoryService diseaseHistoryService;
    private static String CHAT_DEFAULT_ERROR_MESSAGE = "系统正忙，请稍后再试。";
    @Autowired
    private FileAttachmentsRepository fileAttachmentsRepository;

    /**
     * 初始化会话，生成 sessionId，并更新挂号信息中的最新 sessionId
     *
     * @param appointmentId 挂号ID
     * @return 生成的 sessionId
     */
    @Override
    public Long initSession(Long appointmentId) {
        // 查询挂号信息
        HuatuoAppointmentsModel appointment = appointmentsRepository.findById(appointmentId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "挂号信息不存在"));

        // 生成新的 sessionId
        Long sessionId = snowflake.nextId();

        // 更新挂号信息的 latestSessionId 字段
        appointment.setLatestSessionId(sessionId);
        appointmentsRepository.save(appointment);  // 保存更新后的挂号记录

        return sessionId;
    }

    /**
     * 查询挂号信息：根据传入的 ConsultationChatReq 对象中的 appointmentId，从 HuatuoAppointmentsRepository 中查询对应的挂号信息。如果找不到对应的挂号信息，则抛出 ServiceException 异常，提示"挂号信息不存在"。
     * 初始化会话信息：如果查询到的挂号信息中 latestSessionId 为空，则生成新的 sessionId、questionId 和 answerId，并设置到 ConsultationChatVO 对象中。
     */

    @Override
    public SseEmitter consultationChat(ConsultationChatReq consultationChatReq) {
        // 查询挂号信息
        HuatuoAppointmentsModel appointment = appointmentsRepository.findById(consultationChatReq.getAppointmentId())
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "挂号信息不存在"));

        ConsultationChatVO consultationChatVO = OrikaUtils.convert(consultationChatReq, ConsultationChatVO.class);
        consultationChatVO.setQuestionId(snowflake.nextId());
        consultationChatVO.setAnswerId(snowflake.nextId());
        consultationChatVO.setDepartment(appointment.getDepartmentsName());
        if (null == consultationChatReq.getSessionId()) {
            //开启新对话
            consultationChatVO.setSessionId(snowflake.nextId());
            appointment.setLatestSessionId(consultationChatVO.getSessionId());
            appointmentsRepository.save(appointment);
        } else {
            consultationChatVO.setSessionId(consultationChatReq.getSessionId());
            //todo 后期优化
            appointment.setLatestSessionId(consultationChatVO.getSessionId());
            appointmentsRepository.save(appointment);
        }
        return streamQuestion(consultationChatVO);
    }

    private SseEmitter streamQuestion(ConsultationChatVO consultationChatVO) {
        SseEmitter emitter = new SseEmitter(10 * 60 * 1000L); // 超时时间 10 分钟

        // 初步敏感词校验
        if (checkQuestionSpam(consultationChatVO.getQuestion(), consultationChatVO, emitter)) {
            return emitter;
        }

        // 尝试发送头部信息
        try {
            emitter.send(SseEmitter.event().name("header").data(CommonResult.success(consultationChatVO)));
        } catch (IOException e) {
            handleException(emitter, "发送头部信息失败：" + e.getMessage());
            return emitter;
        }

        if(consultationChatVO.getQuestion().contains("身体不舒服")){
            List<String> partList = new ArrayList<>();
            Collections.addAll(partList, "腹部痛", "肺癌", "体检发现异常", "淋巴结肿大", "乳腺癌", "腹痛");
            try {
                String answer = "请问您具体哪里什么不舒服？";
                emitter.send(SseEmitter.event().name("message").data(answer));
                emitter.send(SseEmitter.event().name("single").data(partList));
                saveQuestionAndAnswerAndSession(consultationChatVO, answer);
                emitter.complete();
            } catch (IOException e) {
                handleException(emitter, "发送头部信息失败：" + e.getMessage());
                return emitter;
            }
            return emitter;
        } else if (consultationChatVO.getQuestion().contains("腹部痛")) {
            TreeMap<Integer,String> sessionMap = new TreeMap<>();
            sessionMap.put(1,consultationChatVO.getQuestion());
            cacheFactory.getHashCache().put(String.valueOf(consultationChatVO.getSessionId()), sessionMap);
            List<String> partList = new ArrayList<>();
            Collections.addAll(partList, "一周", "一个月", "三个月", "半年以上");
            try {
                String answer = "请问腹痛发生有多久了？";
                emitter.send(SseEmitter.event().name("message").data(answer));
                emitter.send(SseEmitter.event().name("single").data(partList));
                saveQuestionAndAnswerAndSession(consultationChatVO, answer);
                emitter.complete();
            } catch (IOException e) {
                handleException(emitter, "发送头部信息失败：" + e.getMessage());
                return emitter;
            }
            return emitter;
        }
        TreeMap<Integer,String> sessionMap = (TreeMap<Integer, String>) cacheFactory.getHashCache().get(String.valueOf(consultationChatVO.getSessionId()));
        if (sessionMap!=null && !sessionMap.isEmpty()){
            Map.Entry<Integer, String> lastEntry = sessionMap.lastEntry();
            Integer count = lastEntry.getKey();
            if(count!=0){
                if (count==1) {
                    sessionMap.put(2,consultationChatVO.getQuestion());
                    cacheFactory.getHashCache().put(String.valueOf(consultationChatVO.getSessionId()),sessionMap);
                    try {
                        String answer = "请问您腹痛的具体部位是哪里？";
                        emitter.send(SseEmitter.event().name("message").data(answer));
                        emitter.send(SseEmitter.event().name("show_body").data("show_body"));
                        saveQuestionAndAnswerAndSession(consultationChatVO, answer);
                        emitter.complete();
                    } catch (IOException e) {
                        handleException(emitter, "发送头部信息失败：" + e.getMessage());
                        return emitter;
                    }
                    return emitter;
                } else if (count==2) {
                    sessionMap.put(3,consultationChatVO.getQuestion());
                    cacheFactory.getHashCache().put(String.valueOf(consultationChatVO.getSessionId()),sessionMap);
                    List<String> partList = new ArrayList<>();
                    Collections.addAll(partList, "持续性的", "间歇性的");
                    try {
                        String answer = "请问你的腹痛是下列那种类型的疼痛呢？";
                        emitter.send(SseEmitter.event().name("message").data(answer));
                        emitter.send(SseEmitter.event().name("single").data(partList));
                        saveQuestionAndAnswerAndSession(consultationChatVO, answer);
                        emitter.complete();
                    } catch (IOException e) {
                        handleException(emitter, "发送头部信息失败：" + e.getMessage());
                        return emitter;
                    }
                    return emitter;
                } else if (count==3) {
                    sessionMap.put(4,consultationChatVO.getQuestion());
                    cacheFactory.getHashCache().put(String.valueOf(consultationChatVO.getSessionId()),sessionMap);
                    List<String> partList = new ArrayList<>();
                    Collections.addAll(partList, "钝痛","隐痛","胀痛","绞痛");
                    try {
                        String answer = "请问你的腹痛是钝痛、隐痛、胀痛还是绞痛呢";
                        emitter.send(SseEmitter.event().name("message").data(answer));
                        emitter.send(SseEmitter.event().name("single").data(partList));
                        saveQuestionAndAnswerAndSession(consultationChatVO, answer);
                        emitter.complete();
                    } catch (IOException e) {
                        handleException(emitter, "发送头部信息失败：" + e.getMessage());
                        return emitter;
                    }
                    return emitter;
                } else if (count==4) {
                    sessionMap.put(5,consultationChatVO.getQuestion());
                    cacheFactory.getHashCache().put(String.valueOf(consultationChatVO.getSessionId()),sessionMap);
                    List<String> partList = new ArrayList<>();
                    Collections.addAll(partList, "高血压", "糖尿病","心脑血管疾病");
                    try {
                        String answer = "请问您是否有以下疾病史：";
                        emitter.send(SseEmitter.event().name("message").data(answer));
                        emitter.send(SseEmitter.event().name("multiple").data(partList));
                        saveQuestionAndAnswerAndSession(consultationChatVO, answer);
                        emitter.complete();
                    } catch (IOException e) {
                        handleException(emitter, "发送头部信息失败：" + e.getMessage());
                        return emitter;
                    }
                    return emitter;
                } else if (count==5) {
                    sessionMap.put(6,consultationChatVO.getQuestion());
                    cacheFactory.getHashCache().put(String.valueOf(consultationChatVO.getSessionId()),sessionMap);
                    List<String> partList = new ArrayList<>();
                    Collections.addAll(partList, "青霉素过敏", "头孢过敏","海鲜过敏");
                    try {
                        String answer = "您是否有过敏史，如食物过敏或药物过敏?";
                        emitter.send(SseEmitter.event().name("message").data(answer));
                        emitter.send(SseEmitter.event().name("multiple").data(partList));
                        saveQuestionAndAnswerAndSession(consultationChatVO, answer);
                        emitter.complete();
                    } catch (IOException e) {
                        handleException(emitter, "发送头部信息失败：" + e.getMessage());
                        return emitter;
                    }
                    return emitter;
                } else if (count==6) {
                    sessionMap.put(7,consultationChatVO.getQuestion());
                    cacheFactory.getHashCache().put(String.valueOf(consultationChatVO.getSessionId()),sessionMap);
                    try {
                        Snowflake snowflake = new Snowflake();
                        consultationChatVO.setReportId(snowflake.nextId());
                        initConsultationReport(consultationChatVO, new ReportModel());
                        emitter.send(SseEmitter.event().name("message").data("好的，为您生成报告！"));
                        emitter.send(SseEmitter.event().name("report").data(consultationChatVO));
                        saveQuestionAndAnswerAndSession(consultationChatVO, "好的，为您生成报告！");
                        emitter.complete();
                    } catch (IOException e) {
                        handleException(emitter, "发送头部信息失败：" + e.getMessage());
                        return emitter;
                    }
                    return emitter;
                }
            }
        }

        // 构建请求参数
        HashMap<String, Object> param = new HashMap<>();
        param.put("id", consultationChatVO.getQuestionId().toString());
        param.put("pre_consultation_id", consultationChatVO.getSessionId().toString());
        param.put("medical_case_id", consultationChatVO.getAppointmentId().toString());
        param.put("created", System.currentTimeMillis());
        param.put("user_info", diseaseHistoryService.getBasicInfo());
        param.put("messages", getHistory(consultationChatVO.getSessionId(), consultationChatVO.getQuestion()));
        param.put("model", "huatuo-v6");
        param.put("department", consultationChatVO.getDepartment());
        param.put("stream", true);
        log.info("URL: {}, Request Parameters: {}", modelUrl, JSON.toJSON(param));

        // 异步执行SSE请求
        CompletableFuture.runAsync(() -> {
            // 创建自定义的 SSE 监听器
            ConsultationSSEListener listener = new ConsultationSSEListener(emitter, this, consultationChatVO, param);
            log.info("开始执行SSE请求");
            try {
                // 使用OkHttp Utils获取client和request
                OkHttpClient client = OkHttpUtils.getOkHttpClient();
                Request request = OkHttpUtils.getJsonConsultlPostRequest(modelUrl, param);

                // 创建EventSource工厂并启动事件源
                EventSource.Factory factory = EventSources.createFactory(client);
                factory.newEventSource(request, listener);

                // 等待SSE请求结束
                listener.getCountDownLatch().await();
            } catch (Exception e) {
                log.error("请求SSE错误处理", e);
                try {
                    emitter.send(SseEmitter.event().name("error")
                            .data(CommonResult.error(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION.getCode(), CHAT_DEFAULT_ERROR_MESSAGE, e.getMessage())));
                } catch (IOException ex) {
                    log.error("发送错误事件失败", ex);
                }
//                emitter.complete();
            }
        }, seeExecutor);
        return emitter;
    }

    ReportModel initConsultationReport(ConsultationChatVO consultationChatVO, ReportModel reportModel) {
        reportModel.setId(consultationChatVO.getReportId());
        reportModel.setAppointmentId(consultationChatVO.getAppointmentId());
        reportModel.setMedicalRecordsStatus(ReportEnum.LOADING);
        reportModel.setMedicalRecordsSessionId(consultationChatVO.getSessionId());
        reportRepository.save(reportModel);

        // 查询挂号信息
        HuatuoAppointmentsModel appointment = appointmentsRepository.findById(consultationChatVO.getAppointmentId())
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "挂号信息不存在"));
        appointment.setLatestMedicalRecordsId(consultationChatVO.getReportId());
        appointmentsRepository.save(appointment);
        return reportModel;
    }

    /**
     * 生成问诊报告
     *
     * @param appointmentId 挂号ID
     * @return 生成或更新后的报告对象
     */
    public Long generateConsultationReport(Long appointmentId) {
        // Step 1: 校验挂号ID是否存在
        HuatuoAppointmentsModel appointment = appointmentsRepository.findById(appointmentId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "挂号信息不存在"));
        if (null != appointment.getLatestMedicalRecordsId()) {
            ReportModel reportModel = reportRepository.findById(appointment.getLatestMedicalRecordsId())
                    .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "问诊报告不存在"));

            if (cacheFactory.getHashCache().get(String.valueOf(appointment.getLatestSessionId()))!=null){
                MockReportReq mockReportReq = new MockReportReq();
                mockReportReq.setAppointmentId(appointmentId);
                
                // 用于跟踪特殊选项
                Set<String> noneOptions = new HashSet<>();
                Map<String, String> customContents = new HashMap<>();

                TreeMap<Integer,String> sortedMap = (TreeMap<Integer, String>) cacheFactory.getHashCache().get(String.valueOf(appointment.getLatestSessionId()));
                
                // 记录调试信息
                log.info("从缓存获取到的会话数据: {}", sortedMap);

                // 根据问题顺序处理每个回答
                // key=1: 时间问题的回答
                if(sortedMap.containsKey(1)) {
                    String initialQuestion = sortedMap.get(1);
                    log.info("问题1(初始问题)的回答: {}", initialQuestion);
                    // 这是初始问题，不需要设置值
                }

                // 处理第一个问答内容，即用户选择了多久
                if(sortedMap.containsKey(2)) {
                    String timeAnswer = sortedMap.get(2);
                    log.info("问题2(时间)的回答: {}", timeAnswer);
                    mockReportReq.setTime(timeAnswer);
                    if(timeAnswer.contains("以上均没有")) {
                        noneOptions.add("time");
                    }
                }
                
                // 处理第二个问答内容，即用户选择了部位
                if(sortedMap.containsKey(3)) {
                    String positionAnswer = sortedMap.get(3);
                    log.info("问题3(部位)的回答: {}", positionAnswer);
                    mockReportReq.setPainPosition(positionAnswer);
                    if(positionAnswer.contains("以上均没有")) {
                        noneOptions.add("painPosition");
                    }
                }
                
                // 处理第三个问答内容，即用户选择了疼痛持续性
                if(sortedMap.containsKey(4)) {
                    String durationAnswer = sortedMap.get(4);
                    log.info("问题4(持续性)的回答: {}", durationAnswer);
                    mockReportReq.setPainDuration(durationAnswer);
                    if(durationAnswer.contains("以上均没有")) {
                        noneOptions.add("painDuration");
                    }
                }
                
                // 处理第四个问答内容，即用户选择了疼痛类型
                if(sortedMap.containsKey(5)) {
                    String typeAnswer = sortedMap.get(5);
                    log.info("问题5(类型)的回答: {}", typeAnswer);
                    mockReportReq.setPainType(typeAnswer);
                    if(typeAnswer.contains("以上均没有")) {
                        noneOptions.add("painType");
                    }
                }
                
                // 处理第五个问答内容，即用户选择了疾病史
                if(sortedMap.containsKey(6)) {
                    String historyAnswer = sortedMap.get(6);
                    log.info("问题6(疾病史)的回答: {}", historyAnswer);
                    mockReportReq.setPastMedicalHistory(historyAnswer);
                    if(historyAnswer.contains("以上均没有")) {
                        noneOptions.add("pastMedicalHistory");
                    }
                }
                
                // 处理第六个问答内容，即用户选择了过敏史
                if(sortedMap.containsKey(7)) {
                    String allergyAnswer = sortedMap.get(7);
                    log.info("问题7(过敏史)的回答: {}", allergyAnswer);
                    mockReportReq.setAllergyHistory(allergyAnswer);
                    if(allergyAnswer.contains("以上均没有")) {
                        noneOptions.add("allergyHistory");
                    }
                }

                // 再根据类别进行进一步处理
                for (Map.Entry<Integer, String> entry : sortedMap.entrySet()) {
                    log.info("处理缓存条目: key={}, value={}", entry.getKey(), entry.getValue());
                    Set<String> cacheCategories = getCacheCategories(entry.getValue());
                    log.info("解析的类别: {}", cacheCategories);

                    // 检查是否包含特殊选项
                    boolean hasNoneOption = cacheCategories.contains("noneOption");
                    boolean hasOtherOption = cacheCategories.contains("otherOption");
                    
                    // 处理自定义内容
                    String customContent = null;
                    for (String category : cacheCategories) {
                        if (category.startsWith("customContent:")) {
                            customContent = category.substring("customContent:".length());
                            break;
                        }
                    }

                    // 处理时间问题（问题1）
                    if (entry.getKey() == 1) { 
                        mockReportReq.setTime(entry.getValue());
                    }

                    for (String cacheCategory : cacheCategories) {
                        switch (cacheCategory) {
                            case "time":
                                if (hasNoneOption) {
                                    noneOptions.add("time");
                                }
                                mockReportReq.setTime(entry.getValue());
                                break;
                            case "painPosition":
                                if (hasNoneOption) {
                                    noneOptions.add("painPosition");
                                }
                                mockReportReq.setPainPosition(entry.getValue());
                                break;
                            case "painType":
                                if (hasNoneOption) {
                                    noneOptions.add("painType");
                                }
                                mockReportReq.setPainType(entry.getValue());
                                break;
                            case "painDuration":
                                if (hasNoneOption) {
                                    noneOptions.add("painDuration");
                                }
                                mockReportReq.setPainDuration(entry.getValue());
                                break;
                            case "pastMedicalHistory":
                                if (hasNoneOption && customContent != null) {
                                    // 有"以上均没有"和自定义内容，以自定义内容为准
                                    mockReportReq.setPastMedicalHistory(customContent);
                                    customContents.put("pastMedicalHistory", customContent);
                                } else if (hasNoneOption) {
                                    // 只有"以上均没有"，无自定义内容
                                    mockReportReq.setPastMedicalHistory("无");
                                } else {
                                    // 正常选项
                                    mockReportReq.setPastMedicalHistory(entry.getValue());
                                }
                                break;
                            case "allergyHistory":
                                if (hasNoneOption && customContent != null) {
                                    // 有"以上均没有"和自定义内容，以自定义内容为准
                                    mockReportReq.setAllergyHistory(customContent);
                                    customContents.put("allergyHistory", customContent);
                                } else if (hasNoneOption) {
                                    // 只有"以上均没有"，无自定义内容
                                    mockReportReq.setAllergyHistory("无");
                                } else {
                                    // 正常选项
                                    mockReportReq.setAllergyHistory(entry.getValue());
                                }
                                break;
                            case "familyMedicalHistory":
                                if (hasNoneOption && customContent != null) {
                                    // 有"以上均没有"和自定义内容，以自定义内容为准
                                    mockReportReq.setFamilyMedicalHistory(customContent);
                                    customContents.put("familyMedicalHistory", customContent);
                                } else if (hasNoneOption) {
                                    // 只有"以上均没有"，无自定义内容
                                    mockReportReq.setFamilyMedicalHistory("无");
                                } else {
                                    // 正常选项
                                    mockReportReq.setFamilyMedicalHistory(entry.getValue());
                                }
                                break;
                            default:
                                break;
                        }
                    }
                }

                // 根据收集的特殊选项更新报告模板
                String chiefComplaint;
                if (noneOptions.contains("time") || mockReportReq.getTime() == null || mockReportReq.getTime().contains("以上均没有")) {
                    chiefComplaint = "患者主诉腹部疼痛，发病时间不详";
                } else {
                    chiefComplaint = "患者主诉腹部疼痛，已持续约" + mockReportReq.getTime();
                }
                
                // 构建疾病详情描述
                StringBuilder medicalHistoryBuilder = new StringBuilder("患者");
                
                // 时间描述
                if (noneOptions.contains("time") || mockReportReq.getTime() == null || mockReportReq.getTime().contains("以上均没有")) {
                    medicalHistoryBuilder.append("出现腹痛症状，发病时间不详");
                } else {
                    medicalHistoryBuilder.append("自").append(mockReportReq.getTime()).append("前出现腹痛症状");
                }
                
                // 疼痛部位
                if (noneOptions.contains("painPosition") || mockReportReq.getPainPosition() == null) {
                    medicalHistoryBuilder.append("，疼痛部位不明确");
                } else {
                    medicalHistoryBuilder.append("，疼痛位于").append(mockReportReq.getPainPosition());
                }
                
                // 疼痛类型
                if (noneOptions.contains("painType") || mockReportReq.getPainType() == null || mockReportReq.getPainType().contains("以上均没有")) {
                    medicalHistoryBuilder.append("，疼痛性质不明确");
                } else {
                    medicalHistoryBuilder.append("，为").append(mockReportReq.getPainType());
                }
                
                // 疼痛持续性
                if (noneOptions.contains("painDuration") || mockReportReq.getPainDuration() == null || mockReportReq.getPainDuration().contains("以上均没有")) {
                    medicalHistoryBuilder.append("，疼痛持续性无法确定");
                } else {
                    medicalHistoryBuilder.append("，呈").append(mockReportReq.getPainDuration()).append("发作");
                }
                
                medicalHistoryBuilder.append("，无明显诱因/伴随其他症状。症状至今未缓解，遂前来就诊。");
                String medicalHistory = medicalHistoryBuilder.toString();
                
                // 处理疾病史、过敏史、家族史
                String allergyHistory;
                if (mockReportReq.getAllergyHistory() == null) {
                    allergyHistory = "无";
                } else if (mockReportReq.getAllergyHistory().contains("以上均没有")) {
                    // 检查是否有自定义内容
                    if (mockReportReq.getAllergyHistory().contains(",") || mockReportReq.getAllergyHistory().contains("，")) {
                        // 提取自定义内容
                        String customContent = mockReportReq.getAllergyHistory().replaceAll("以上均没有[,，]\\s*", "").trim();
                        allergyHistory = StringUtils.isNotBlank(customContent) ? customContent : "无";
                    } else {
                        allergyHistory = "无";
                    }
                } else {
                    allergyHistory = mockReportReq.getAllergyHistory();
                }
                
                String pastHistory;
                if (mockReportReq.getPastMedicalHistory() == null) {
                    pastHistory = "无";
                } else if (mockReportReq.getPastMedicalHistory().contains("以上均没有")) {
                    // 检查是否有自定义内容
                    if (mockReportReq.getPastMedicalHistory().contains(",") || mockReportReq.getPastMedicalHistory().contains("，")) {
                        // 提取自定义内容
                        String customContent = mockReportReq.getPastMedicalHistory().replaceAll("以上均没有[,，]\\s*", "").trim();
                        pastHistory = StringUtils.isNotBlank(customContent) ? customContent : "无";
                    } else {
                        pastHistory = "无";
                    }
                } else {
                    pastHistory = mockReportReq.getPastMedicalHistory();
                }
                
                String familyHistory;
                if (mockReportReq.getFamilyMedicalHistory() == null) {
                    familyHistory = "无";
                } else if (mockReportReq.getFamilyMedicalHistory().contains("以上均没有")) {
                    // 检查是否有自定义内容
                    if (mockReportReq.getFamilyMedicalHistory().contains(",") || mockReportReq.getFamilyMedicalHistory().contains("，")) {
                        // 提取自定义内容
                        String customContent = mockReportReq.getFamilyMedicalHistory().replaceAll("以上均没有[,，]\\s*", "").trim();
                        familyHistory = StringUtils.isNotBlank(customContent) ? customContent : "无";
                    } else {
                        familyHistory = "无";
                    }
                } else {
                    familyHistory = mockReportReq.getFamilyMedicalHistory();
                }

                reportModel.setChiefComplaint(chiefComplaint);
                reportModel.setPresentIllness(medicalHistory);
                reportModel.setPastMedicalHistory(pastHistory);
                reportModel.setAllergyHistory(allergyHistory);
                reportModel.setFamilyMedicalHistory(familyHistory);
                reportModel.setMedicalRecordsStatus(ReportEnum.PENDING);
                cacheFactory.getHashCache().remove(String.valueOf(appointment.getLatestSessionId()));
            }else {
                reportModel = fetchAndUpdateReport(reportModel, appointment);
            }

            // 保存报告
            reportRepository.save(reportModel);

            // 2.1 更新文件表中的文件对应的reportId
            Long reportId = reportModel.getId();
            List<FileAttachmentsModel> fileAttachmentsModelList = fileAttachmentsRepository.findBySessionId(reportModel.getMedicalRecordsSessionId());
            // 2.2 批量更新 reportId
            fileAttachmentsModelList.forEach(file -> file.setReportId(reportId));
            // 2.3批量保存到数据库
            fileAttachmentsRepository.saveAll(fileAttachmentsModelList);

            return reportModel.getId();
        } else {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "挂号信息不存在");
        }
    }

    /**
     * 调用外部接口生成或更新问诊报告
     *
     * @param reportModel 当前报告对象
     * @param appointment 挂号信息
     * @return 更新后的报告ID
     */
    public ReportModel fetchAndUpdateReport(ReportModel reportModel, HuatuoAppointmentsModel appointment) {
        try {
            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams(reportModel, appointment);

            // 构造 POST 请求
            String response = OkHttpUtils.builder()
                    .url(modelReportUrl)
                    .addHeader("Content-Type", "application/json")
                    // 发送 JSON 格式数据
                    .jsonPost(requestParams)
                    // 同步请求
                    .sync();

            // 解析返回数据
            JSONObject responseJson = JSONUtil.parseObj(response);
            return updateReportFromResponse(reportModel, responseJson);
        } catch (Exception e) {
            log.error("生成问诊报告失败: {}", e.getMessage(), e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "生成问诊报告失败");
        }
    }


    /**
     * 构建外部接口请求参数
     */
    private Map<String, Object> buildRequestParams(ReportModel reportModel, HuatuoAppointmentsModel appointment) {
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("id", reportModel.getMedicalRecordsSessionId().toString());
        requestParams.put("created", System.currentTimeMillis());
        requestParams.put("model", "huatuo-v6");
        requestParams.put("medical_case_id", appointment.getId().toString());
        requestParams.put("department", appointment.getDepartmentsName());
        requestParams.put("pre_consultation_id", reportModel.getMedicalRecordsSessionId().toString());

        requestParams.put("messages", getHistory(reportModel.getMedicalRecordsSessionId(), null));
        //个人信息
        requestParams.put("user_info", diseaseHistoryService.getBasicInfo());
        return requestParams;
    }

    /**
     * 根据外部接口响应更新报告对象
     */
    private ReportModel updateReportFromResponse(ReportModel reportModel, JSONObject responseJson) {
        reportModel.setChiefComplaint(responseJson.getStr("chief_complaint"));
        reportModel.setPresentIllness(responseJson.getStr("present_illness"));
        reportModel.setPastMedicalHistory(responseJson.getStr("past_history"));
        reportModel.setAllergyHistory(responseJson.getStr("allergy_history"));
        reportModel.setFamilyMedicalHistory(responseJson.getStr("family_history"));
        reportModel.setMedicalRecordsStatus(ReportEnum.PENDING);
        return reportModel;
    }


    boolean checkQuestionSpam(String id, String content) {
        if (StringUtils.isEmpty(content)) {
            return false;
        }
        Boolean antispam = antispamUtil.checkAntispam(id, content);
        return (antispam != null && antispam);
    }

    private boolean checkQuestionSpam(String question, ConsultationChatVO chatVO, SseEmitter emitter) {
        Boolean aBoolean = antispamUtil.checkAntispam(snowflake.nextIdStr(), question);
        if (aBoolean) {
            // 给出默认答案
            String answer = "对不起，这个问题超出了我可以处理的范围。您能否提出其他类型的问题？";
            handleSpamResponse(emitter, answer);
            chatVO.setQuestionId(snowflake.nextId());
            saveQuestionAndAnswerAndSession(chatVO, answer);
        }
        return aBoolean;
    }

    private void handleSpamResponse(SseEmitter emitter, String message) {
        try {
            emitter.send(SseEmitter.event().name("spam")
                    .data(message));
            emitter.complete();
        } catch (IOException e) {
            log.error("模型对话出错！", e);
            handleException(emitter, ServiceExceptionUtil.getStackTrace(e));
        }
    }

    private void handleException(SseEmitter emitter, String errorTrace) {
        try {
            emitter.send(
                    SseEmitter.event()
                            .name("error")
                            .data(
                                    CommonResult.error(
                                            OUTER_SERVER_EXCEPTION.getCode(), CHAT_DEFAULT_ERROR_MESSAGE, errorTrace)));
            emitter.complete();
        } catch (ClientAbortException clientAbortException) {
            log.error("连接关闭失败！", clientAbortException);
        } catch (IOException e) {
            log.error("处理异常", e);
        }
    }

    private List<ConsultationChatHistoryItem> getHistory(Long sessionId, String currentQuestionContent) {
        List<ConsultationChatHistoryItem> result = new ArrayList<>();

        // Step 1: 查询本次会话的所有问题（按时间升序 asc）
        QConsultationChatQuestionModel qChatQuestionModel = QConsultationChatQuestionModel.consultationChatQuestionModel;
        BooleanBuilder questionBuilder = new BooleanBuilder();
        questionBuilder.and(qChatQuestionModel.sessionId.eq(sessionId));
        List<Tuple> questionTuple = queryFactory.select(qChatQuestionModel.id, qChatQuestionModel.content)
                .from(qChatQuestionModel)
                .where(questionBuilder)
                .orderBy(qChatQuestionModel.createTime.asc()) // 使用时间正序
                .limit(200)
                .fetch();

        // Step 2: 查询本次会话的所有答案（按时间升序 asc）
        QConsultationChatAnswerModel qChatAnswerModel = QConsultationChatAnswerModel.consultationChatAnswerModel;
        BooleanBuilder answerBuilder = new BooleanBuilder();
        answerBuilder.and(qChatAnswerModel.sessionId.eq(sessionId));
        List<Tuple> answerTuple = queryFactory.select(qChatAnswerModel.questionId, qChatAnswerModel.content)
                .from(qChatAnswerModel)
                .where(answerBuilder)
                .orderBy(qChatAnswerModel.createTime.asc()) // 使用时间正序
                .limit(200)
                .fetch();

        // 将答案存入 Map 中，方便通过 questionId 获取对应答案
        Map<Long, String> answers = new HashMap<>();
        for (Tuple tuple : answerTuple) {
            answers.put(tuple.get(qChatAnswerModel.questionId), tuple.get(qChatAnswerModel.content));
        }

        // Step 3: 组装历史问题和答案（按照最早到最新的顺序）
        for (Tuple tuple : questionTuple) {
            Long questionId = tuple.get(qChatQuestionModel.id);
            String questionContent = tuple.get(qChatQuestionModel.content);

            // 如果当前问题ID与待处理问题相同（curQuestionId），根据需求决定是否跳过或直接添加
            // 如果不想重复当前问题，可以跳过; 如果希望保留历史中的该问题也没问题
            // 这里不跳过，保留历史完整性
            // if (curQuestionId != null && questionId.equals(curQuestionId)) {
            //     continue;
            // }

            // 添加用户的问题
            ConsultationChatHistoryItem userMessage = new ConsultationChatHistoryItem();
            userMessage.setRole("user");
            userMessage.setContent(questionContent);
            result.add(userMessage);

            // 如果有对应的答案
            if (answers.containsKey(questionId)) {
                ConsultationChatHistoryItem systemMessage = new ConsultationChatHistoryItem();
                systemMessage.setRole("assistant");
                systemMessage.setContent(answers.get(questionId));
                result.add(systemMessage);
            }
        }

        // Step 4: 将本次当前请求的问题加入历史记录结尾（即最新的位置）
        // 通常currentQuestionContent是本次对话最新用户请求，还未在数据库记录中出现
        if (currentQuestionContent != null) {
            ConsultationChatHistoryItem currentUserMessage = new ConsultationChatHistoryItem();
            currentUserMessage.setRole("user");
            currentUserMessage.setContent(currentQuestionContent);
            result.add(currentUserMessage);
        }

        // Step 5: 若记录超过200条，截取前200条
        // 因为已经正序，从最早到最新，如果只允许最多200条，则可保留最后200条比较合理
        // 看业务需求，是保留最初200条还是保留最新200条。
        // 若希望保留最新的200条记录，可如下处理：
        if (result.size() > 200) {
            // 截取最后200条记录（即最新的200条）
            return result.subList(result.size() - 200, result.size());
        }

        return result;
    }


    public void saveQuestionAndAnswerAndSession(ConsultationChatVO consultationChatVO, String answer) {
        log.info("模型回答：" + answer);
        // 保存问题
        ConsultationChatQuestionModel chatQuestionModel = new ConsultationChatQuestionModel();
        chatQuestionModel.setId(consultationChatVO.getQuestionId());
        chatQuestionModel.setContent(consultationChatVO.getQuestion());
        chatQuestionModel.setSessionId(consultationChatVO.getSessionId());
        consultationChatQuestionRepository.save(chatQuestionModel);

        // 处理答案
        ConsultationChatAnswerModel chatAnswerModel;
        Optional<ConsultationChatAnswerModel> optionalChatAnswerModel = consultationChatAnswerRepository.findById(consultationChatVO.getAnswerId());
        if (optionalChatAnswerModel.isPresent()) {
            // 如果答案存在，更新内容并清空点赞
            chatAnswerModel = optionalChatAnswerModel.get();
        } else {
            // 如果答案不存在，创建新的答案记录
            chatAnswerModel = new ConsultationChatAnswerModel();
            chatAnswerModel.setId(consultationChatVO.getAnswerId());
            chatAnswerModel.setContent(answer);
            chatAnswerModel.setQuestionId(consultationChatVO.getQuestionId());
            chatAnswerModel.setSessionId(consultationChatVO.getSessionId());
        }

        chatAnswerModel.setContent(answer);
        consultationChatAnswerRepository.save(chatAnswerModel);

        // 保存会话
        Optional<ConsultationChatSessionModel> optional = consultationChatSessionRepository.findById(consultationChatVO.getSessionId());
        if (!optional.isPresent()) {
            ConsultationChatSessionModel chatSessionModel = new ConsultationChatSessionModel();
            chatSessionModel.setId(consultationChatVO.getSessionId());
            chatSessionModel.setUserId(LoginContextHolder.getLoginUserId());
            chatSessionModel.setAppointmentId(consultationChatVO.getAppointmentId());
            consultationChatSessionRepository.save(chatSessionModel);
        }
    }

    @Override
    public List<ConsultationChatHistoryResp> searchHistoryByAppointmentId(Long appointmentId) {
        // Step 1: 查询该挂号ID对应的所有会话ID
        QConsultationChatSessionModel qChatSessionModel = QConsultationChatSessionModel.consultationChatSessionModel;
        BooleanBuilder sessionBuilder = new BooleanBuilder();
        // 使用挂号ID查询所有会话ID
        sessionBuilder.and(qChatSessionModel.appointmentId.eq(appointmentId));
        // 获取所有会话ID
        List<Long> sessionIds = queryFactory.select(qChatSessionModel.id)
                .from(qChatSessionModel)
                .where(sessionBuilder)
                .fetch();  // 直接返回 List<Long> 类型

        // 如果没有会话ID，直接返回空集合
        if (sessionIds.isEmpty()) {
            // 返回空集合
            return Collections.emptyList();
        }

        // Step 2: 查询所有问题和答案（根据所有的 sessionId）
        QConsultationChatQuestionModel qChatQuestionModel = QConsultationChatQuestionModel.consultationChatQuestionModel;
        QConsultationChatAnswerModel qChatAnswerModel = QConsultationChatAnswerModel.consultationChatAnswerModel;

        // 使用sessionId查询问题
        List<ConsultationChatQuestionModel> questionList = queryFactory.selectFrom(qChatQuestionModel)
                .where(qChatQuestionModel.sessionId.in(sessionIds))
                .orderBy(qChatQuestionModel.createTime.asc())  // 按照创建时间升序
                .fetch();

        // 使用sessionId查询答案
        List<ConsultationChatAnswerModel> answerList = queryFactory.selectFrom(qChatAnswerModel)
                .where(qChatAnswerModel.sessionId.in(sessionIds))
                .orderBy(qChatAnswerModel.createTime.asc())  // 按照创建时间升序
                .fetch();

        // 如果没有问题和答案，直接返回空集合
        if (questionList.isEmpty() && answerList.isEmpty()) {
            return Collections.emptyList();  // 返回空集合
        }

        // Step 3: 将答案存入 Map 中，方便通过问题ID查找答案
        Map<Long, String> answers = new HashMap<>();
        for (ConsultationChatAnswerModel answer : answerList) {
            answers.put(answer.getQuestionId(), answer.getContent());
        }

        // Step 4: 组装问题和答案
        List<ConsultationChatHistoryResp> result = new ArrayList<>();
        for (ConsultationChatQuestionModel question : questionList) {
            Long questionId = question.getId();
            String questionContent = question.getContent();

            // 如果答案存在，则配对问题和答案
            if (answers.containsKey(questionId)) {
                ConsultationChatHistoryResp chatHistoryResp = new ConsultationChatHistoryResp();

                // 设置问题内容
                ConsultationChatQuestionBase questionBase = new ConsultationChatQuestionBase();
                questionBase.setId(questionId);
                questionBase.setContent(questionContent);
                chatHistoryResp.setConsultationChatQuestionBase(questionBase);

                // 设置答案内容
                ConsultationChatAnswerBase answerBase = new ConsultationChatAnswerBase();
                answerBase.setContent(answers.get(questionId));
                chatHistoryResp.setConsultationChatAnswerBase(answerBase);

                result.add(chatHistoryResp);
            }

            // 如果已达到200条记录，停止添加
            if (result.size() >= 200) {
                break;
            }
        }

        // 返回查询的结果（最多200条）
        return result;  // 此时 result 已经被限制为最多200条
    }


    @Override
    public List<ConsultationChatHistoryResp> searchHistoryBySessionId(Long sessionId) {
        // Step 1: 检查 sessionId 是否为空
        if (sessionId == null) {
            log.warn("查询历史记录时传入的 sessionId 为空");
            return Collections.emptyList();
        }

        // Step 2: 查询本次会话的所有问题和答案
        QConsultationChatQuestionModel qChatQuestionModel = QConsultationChatQuestionModel.consultationChatQuestionModel;
        QConsultationChatAnswerModel qChatAnswerModel = QConsultationChatAnswerModel.consultationChatAnswerModel;
        QFileAttachmentsModel qFileAttachmentsModel = QFileAttachmentsModel.fileAttachmentsModel;

        List<Tuple> queryResults;
        List<Tuple> queryImageResults;
        try {
            queryResults = queryFactory
                    .select(qChatQuestionModel.id, qChatQuestionModel.content, qChatAnswerModel.content)
                    .from(qChatQuestionModel)
                    .leftJoin(qChatAnswerModel).on(qChatQuestionModel.id.eq(qChatAnswerModel.questionId))
                    .where(qChatQuestionModel.sessionId.eq(sessionId))
                    .orderBy(qChatQuestionModel.createTime.asc())  // 按问题创建时间升序排列
                    .limit(200)  // 限制最多返回 200 条
                    .fetch();

            queryImageResults = queryFactory
                    .select(qFileAttachmentsModel.id, qFileAttachmentsModel.filePath)
                    .from(qFileAttachmentsModel)
                    .where(qFileAttachmentsModel.sessionId.eq(sessionId))
                    .orderBy(qFileAttachmentsModel.createTime.asc())  // 按问题创建时间升序排列
                    .limit(9)  // 限制最多返回 9 条
                    .fetch();
        } catch (Exception e) {
            log.error("查询会话 {} 的问题和答案失败", sessionId, e);
            return Collections.emptyList();  // 查询失败时返回空集合
        }

        // 如果查询结果为空，直接返回空集合
        if (queryResults.isEmpty()) {
            log.warn("会话 {} 没有历史记录。", sessionId);
            return Collections.emptyList();
        }

        // Step 3: 组装问题和答案
        List<ConsultationChatHistoryResp> result = new ArrayList<>();
        for (Tuple tuple : queryResults) {
            Long questionId = tuple.get(qChatQuestionModel.id);
            String questionContent = tuple.get(qChatQuestionModel.content);
            String answerContent = tuple.get(qChatAnswerModel.content);

            ConsultationChatHistoryResp chatHistoryResp = new ConsultationChatHistoryResp();

            // 设置问题内容
            ConsultationChatQuestionBase questionBase = new ConsultationChatQuestionBase();
            questionBase.setId(questionId);
            questionBase.setContent(questionContent);
            chatHistoryResp.setConsultationChatQuestionBase(questionBase);

            // 设置答案内容（如果存在）
            if (answerContent != null) {
                ConsultationChatAnswerBase answerBase = new ConsultationChatAnswerBase();
                answerBase.setContent(answerContent);
                chatHistoryResp.setConsultationChatAnswerBase(answerBase);
            }

            result.add(chatHistoryResp);

            // 如果已达到 200 条记录，停止添加
            if (result.size() >= 200) {
                break;
            }
        }

        // Step 4: 设置图片列表
        ConsultationChatHistoryResp chatHistoryImageResp = new ConsultationChatHistoryResp();
        List<ConsultationChatImageBase> consultationChatImageBaseList = new ArrayList<>();
        for (Tuple tuple : queryImageResults) {
            ConsultationChatImageBase imageBase = new ConsultationChatImageBase();
            imageBase.setId(tuple.get(qFileAttachmentsModel.id));
            imageBase.setFilePath(tuple.get(qFileAttachmentsModel.filePath));
            consultationChatImageBaseList.add(imageBase);
        }
        if (!consultationChatImageBaseList.isEmpty()) {
            chatHistoryImageResp.setConsultationChatImageBaseList(consultationChatImageBaseList);
            result.add(chatHistoryImageResp);
        }

        // 返回查询结果（最多 200 条）
        return result;
    }


}
