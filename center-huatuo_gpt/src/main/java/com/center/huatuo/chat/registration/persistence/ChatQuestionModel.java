package com.center.huatuo.chat.registration.persistence;

import com.center.framework.db.annotation.GenerateId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Date: 2023/3/13 19:51
 * @Description: 对话的用户问题
 */
@Data
@Table(name = "chat_question")
@Entity
public class ChatQuestionModel {

    @Id
    @Column(name = "id", nullable = false, updatable = false)
    @GenerateId
    private Long id;
    /**
     * 对话内容
     */
    @Column(name = "content")
    private String content;
    /**
     * 问题分类
     */
    @Column(name = "clazz")
    private int clazz;
    /**
     * 对话id
     */
    @Column(name = "session_id")
    private Long sessionId;
    /**
     * 创建时间
     */
    @Column(name = "ctime")
    private Date ctime;

    /**
     * 用户ID
     */
    @Column(name = "user_id")
    private Long userId;
}
