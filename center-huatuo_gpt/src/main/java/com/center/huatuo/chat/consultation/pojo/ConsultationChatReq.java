package com.center.huatuo.chat.consultation.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "问诊对话")
public class ConsultationChatReq {
    @NotBlank(message = "问题不能为空")
    @Schema(description = "问题内容")
    private String question;

    @Schema(description = "挂号id")
    @NotNull(message = "挂号id不能为空")
    private Long appointmentId;

    @Schema(description = "对话SessionID")
    private Long sessionId;
}
