package com.center.huatuo.chat.registration.persistence;

import com.center.framework.db.annotation.GenerateId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Date: 2023/3/13 19:51
 * @Description: 对话的回答
 */
@Data
@Table(name = "chat_answer")
@Entity
public class ChatAnswerModel {

    @Id
    @Column(name = "id", nullable = false, updatable = false)
    @GenerateId
    private Long id;
    /**
     * 回答内容
     */

    @Column(name = "content")
    private String content;
    /**
     * 创建时间
     */
    @Column(name = "ctime")
    private Date ctime;
    /**
     * 回答来源
     */
    @Column(name = "source")
    private int source;
    /**
     * 评分，-1 踩，0 默认，1 赞
     */
    @Column(name = "mark")
    private int mark;
    /**
     * 问题id
     */
    @Column(name = "question_id")
    private Long questionId;
    /**
     * 对话id
     */
    @Column(name = "session_id")
    private Long sessionId;
    /**
     * 未进行后处理的answer
     */
    @Column(name = "raw_answer")
    private String rawAnswer;
    /**
     * 敏感词
     */
    @Column(name = "sensitive_words")
    private String sensitiveWords;

}
