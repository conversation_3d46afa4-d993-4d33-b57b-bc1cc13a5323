package com.center.huatuo.chat.consultation.service;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.CommonResult;
import com.center.huatuo.chat.consultation.pojo.ConsultationChatVO;
import com.center.huatuo.report.enumerate.ReportEnum;
import com.center.huatuo.report.persistence.ReportModel;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION;

@Slf4j
public class ConsultationSSEListener extends EventSourceListener {

    private final SseEmitter emitter;
    private final ConsultationChatServiceImpl service;
    private final ConsultationChatVO consultationChatVO;
    private final Map<String, Object> param;
    private final CountDownLatch countDownLatch = new CountDownLatch(1);

    // 用于存储最终生成的回答内容
    private final StringBuilder builder = new StringBuilder();
    // 用于生成报告的Model
    private final ReportModel reportModel = new ReportModel();

    // 标记对话是否已经结束（无论成功还是出错）
    private boolean completed = false;

    private static final String CHAT_DEFAULT_ERROR_MESSAGE = "系统正忙，请稍后再试。";
    private static final String CHAT_DEFAULT_SPAMERROR_MESSAGE = "\"我们的平台致力于提供一个安全、尊重的环境，某些话题可能不适宜讨论。我会很乐意帮助您解答其他问题，谢谢您的理解和配合。\"";

    public ConsultationSSEListener(SseEmitter emitter,
                                   ConsultationChatServiceImpl service,
                                   ConsultationChatVO consultationChatVO,
                                   Map<String, Object> param) {
        this.emitter = emitter;
        this.service = service;
        this.consultationChatVO = consultationChatVO;
        this.param = param;
    }

    @Override
    public void onOpen(EventSource eventSource, Response response) {
        log.info("SSE连接已建立，与模型对话开始。SessionID: {}, VO: {}", consultationChatVO.getSessionId(), consultationChatVO);
    }

    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {
        // 如果已完成就不再处理事件
        if (completed) {
            log.warn("已完成的对话又收到事件，忽略。EventType: {}, Data: {}", type, data);
            return;
        }

        String eventType = (type == null) ? "" : type;
        log.info("收到事件类型: {}, 数据: {}", eventType, data);

        try {
            if (eventType.startsWith("message")) {
                handleMessageEvent(data);
            } else if (eventType.startsWith("dialogue")) {
                handleEndEvent(data);
            } else if (eventType.startsWith("show_")) {
                handleBodyEvent(eventType,data);
            } else {
                handleOtherEvent(data);
            }

        } catch (Exception e) {
            log.error("解析流事件出错，EventType: {}, Data: {}, 异常: {}", eventType, data, e.getMessage(), e);
            handleException("解析流事件出错：" + e.getMessage());
            eventSource.cancel(); // 中断事件源，防止继续出错
        }
    }

    @Override
    public void onClosed(EventSource eventSource) {
        log.info("EventSource已关闭。SessionID: {}, 最终输出: {}", consultationChatVO.getSessionId(), builder.toString());

        // 如果已经完成（例如在 spam、error 时完成过），则不重复处理
        if (!completed) {
            try {
                service.saveQuestionAndAnswerAndSession(consultationChatVO, builder.toString());
            } catch (Exception e) {
                log.error("在onClosed中保存对话记录失败: {}", e.getMessage(), e);
                // 即使保存失败，也要结束SSE连接
            } finally {
                countDownLatch.countDown();
                safeComplete();
            }
        } else {
            // 已经完成过了，不再重复
            countDownLatch.countDown();
        }
    }

    @Override
    public void onFailure(EventSource eventSource, Throwable t, Response response) {
        String errorMessage = "流式数据接收失败";
        if (t != null) {
            log.error(errorMessage, t);
        } else {
            log.error(errorMessage + ": 无异常对象提供");
        }

        // 尝试获取更详细的响应信息
        if (response != null) {
            try {
                int code = response.code();
                String respBody = (response.body() != null) ? response.body().string() : "无响应体";
                log.error("onFailure时的Response: code={}, body={}", code, respBody);
                errorMessage += "，响应码：" + code;
            } catch (IOException e) {
                log.error("读取响应体失败：{}", e.getMessage(), e);
            }
        }

        // 通知前端并关闭连接
        handleException(errorMessage);
        countDownLatch.countDown();
    }

    /**
     * 处理message事件
     */
    private void handleMessageEvent(String eventData) throws IOException {
        JSONObject jsonObject = JSONUtil.parseObj(eventData);
        JSONObject delta = jsonObject.getByPath("choices[0].delta", JSONObject.class);
        String content = (delta != null) ? delta.getStr("content") : null;

        if (StringUtils.isNotEmpty(content)) {
            builder.append(content);

            // 二次敏感词校验（根据业务需求可优化逻辑）
            Snowflake snowflake = new Snowflake();
            if (builder.length() > 20 && builder.length() % 3 == 0 &&
                    service.checkQuestionSpam(snowflake.nextIdStr(), builder.toString())) {
                handleSpamResponse(CHAT_DEFAULT_SPAMERROR_MESSAGE);
                return;
            }

            emitter.send(SseEmitter.event().name("message").data(content));
        } else {
            log.warn("message事件中未找到content字段。Data: {}", eventData);
        }
    }

    /**
     * 处理end事件
     */
    private void handleEndEvent(String eventData) throws IOException {
        log.info("收到dialogue_end事件，data={}", eventData);
        if (isCompleteJson(eventData)) {
            Snowflake snowflake = new Snowflake();
            consultationChatVO.setReportId(snowflake.nextId());
            service.initConsultationReport(consultationChatVO, reportModel);

            emitter.send(SseEmitter.event().name("report").data(consultationChatVO));
        } else {
            log.warn("dialogue_end事件数据非完整JSON: {}", eventData);
        }
    }
    private void handleBodyEvent(String eventType,String eventData) throws IOException {
        log.info("收到show_事件，data={}", eventData);
        if (isCompleteJson(eventData)) {
            emitter.send(SseEmitter.event().name(eventType).data(eventType));
        } else {
            log.warn("show_事件数据非完整JSON: {}", eventData);
        }
    }
    /**
     * 处理其他类型事件
     */
    private void handleOtherEvent(String eventData) throws IOException {
        if (!isCompleteJson(eventData)) {
            log.warn("其他类型事件数据非完整JSON: {}", eventData);
            return;
        }

        JSONObject jsonObject = JSONUtil.parseObj(eventData);
        JSONObject delta = jsonObject.getByPath("choices[0].delta", JSONObject.class);
        String content = (delta != null) ? delta.getStr("content") : null;

        if (StringUtils.isNotEmpty(content)) {
            builder.append(content);
            Snowflake snowflake = new Snowflake();
            if (builder.length() > 20 && builder.length() % 3 == 0 &&
                    service.checkQuestionSpam(snowflake.nextIdStr(), builder.toString())) {
                service.saveQuestionAndAnswerAndSession(consultationChatVO, builder.toString());
                handleSpamResponse(CHAT_DEFAULT_SPAMERROR_MESSAGE);
                return;
            }

            emitter.send(SseEmitter.event().name("message").data(content));
        } else {
            log.warn("其他事件中未找到content字段。Data: {}", eventData);
        }
    }

    /**
     * 处理敏感词情况，立即结束SSE
     */
    private void handleSpamResponse(String message) {
        if (completed) {
            log.warn("已经完成的SSE尝试再次spam结束，忽略");
            return;
        }

        try {
            service.saveQuestionAndAnswerAndSession(consultationChatVO, builder.toString());
            emitter.send(SseEmitter.event().name("spam").data(message));
            safeComplete();
        } catch (IOException e) {
            log.error("spam事件写出失败", e);
            handleException("spam事件写出失败：" + e.getMessage());
        }
    }

    /**
     * 处理异常并向前端发送error事件
     */
    private void handleException(String errorTrace) {
        if (completed) {
            log.warn("已完成的SSE再次试图发送error，忽略");
            return;
        }

        try {
            emitter.send(
                    SseEmitter.event()
                            .name("error")
                            .data(
                                    CommonResult.error(
                                            OUTER_SERVER_EXCEPTION.getCode(),
                                            CHAT_DEFAULT_ERROR_MESSAGE,
                                            errorTrace)));
        } catch (IOException e) {
            log.error("处理异常时发送失败: {}", e.getMessage(), e);
        } finally {
            safeComplete();
        }
    }

    /**
     * 安全完成SSE，不重复完成
     */
    private void safeComplete() {
        if (!completed) {
            emitter.complete();
            completed = true;
        }
    }

    public CountDownLatch getCountDownLatch() {
        return countDownLatch;
    }

    private boolean isCompleteJson(String json) {
        try {
            JSONUtil.parseObj(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
