package com.center.huatuo.chat.consultation.persistence;

import com.center.framework.db.annotation.GenerateId;
import com.center.framework.db.core.BaseModel;
import com.center.framework.db.listener.IgnoreNullEventListener;
import lombok.Data;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

/**
 * @Date: 2023/3/13 19:51
 * @Description: 对话的用户问题
 */
@Data
@Table(name = "huatuo_consultation_chat_question")
@Entity
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class ConsultationChatQuestionModel extends BaseModel {

    /**
     * 对话内容
     */
    @Column(name = "content")
    private String content;
    /**
     * 问题分类
     */
    @Column(name = "clazz")
    private int clazz;
    /**
     * 对话id
     */
    @Column(name = "session_id")
    private Long sessionId;


}
