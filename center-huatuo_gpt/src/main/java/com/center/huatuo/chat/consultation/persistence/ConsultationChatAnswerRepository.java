package com.center.huatuo.chat.consultation.persistence;

import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ConsultationChatAnswerRepository extends JoinFetchCapableQueryDslJpaRepository<ConsultationChatAnswerModel,Long> {

    List<ConsultationChatAnswerModel> findBySessionId(Long sessionId);

}
