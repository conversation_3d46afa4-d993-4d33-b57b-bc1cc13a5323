package com.center.huatuo.chat.registration.service;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.center.huatuo.chat.registration.persistence.*;
import com.center.huatuo.chat.registration.pojo.*;
import com.center.huatuo.common.utils.AntispamUtil;
import com.center.huatuo.common.utils.OkHttpUtils;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.CodingErrorAction;
import java.util.*;
import java.util.concurrent.Executor;

@Service
@Slf4j
public class ChatServiceImpl implements ChatService{

    @Resource
    AntispamUtil antispamUtil;

    @Resource
    Snowflake snowflake;

    @Autowired
    @Qualifier("taskScheduler")
    private Executor seeExecutor;

    public static final String WARN_ANSWER = "我们的平台致力于提供一个安全、尊重的环境，某些话题可能不适宜讨论。我会很乐意帮助您解答其他问题，谢谢您的理解和配合。";
    public static final String DEFAULT_ANSWER = "对不起，这个问题超出了我可以处理的范围。您能否提出其他类型的问题？";

    @Resource
    private JPAQueryFactory queryFactory;

    @Resource
    private ChatQuestionRepository chatQuestionRepository;

    @Resource
    private ChatAnswerRepository chatAnswerRepository;

    @Resource
    private ChatSessionRepository chatSessionRepository;

    private static String CHAT_DEFAULT_ERROR_MESSAGE = "系统正忙，请稍后再试。";

    @Value("${huatuo-gpt.url}")
    private String url;

    @Override
    @Transactional
    public SseEmitter chat(ChatReq chatReq){
        return streamQuestion(chatReq);
    }


    @Override
    public void thumbsUp(Long id) {
       updateAnswerThumbs(id,1);
    }

    @Override
    public void thumbsDown(Long id) {
        updateAnswerThumbs(id,-1);
    }

    @Override
    public void thumbsCancel(Long id) {
        updateAnswerThumbs(id,0);

    }


    private SseEmitter streamQuestion(ChatReq chatReq){
        SseEmitter emitter = new SseEmitter(10 * 60 * 1000L);
//        todo：单次session对话次数不能超过20次。可以与PM确认这个逻辑是否还需要。

        ChatVO chatVO = new ChatVO();
        chatVO.setQuestion(chatReq.getQuestion());
        chatVO.setQuestionId(snowflake.nextId());
        chatVO.setSessionId(chatReq.getSessionId());
        chatVO.setAnswerId(snowflake.nextId());
        if(checkSpam(chatReq.getQuestion())){
            handleSpamResponse(emitter,DEFAULT_ANSWER);
            chatVO.setAnswer(DEFAULT_ANSWER);
            handleCompletedResponse(emitter,chatVO);
            saveQuestionAndAnswerAndSession(chatVO, StrUtil.EMPTY);
            emitter.complete();
            return emitter;
        }
        HashMap<String, Object> param = new HashMap<>();
        param.put("text", chatReq.getQuestion());
        param.put("history", getHistory(chatVO.getSessionId()));
        param.put("images", new ArrayList<>());
        HashMap<String, Object> config = new HashMap<>();
        config.put("profile_prompt", chatReq.getProfilePrompt());
        config.put("temperature", 0.7);
        config.put("max_new_tokens", 512);
        param.put("config", config);
        log.info("URL: {}, Request Parameters: {}", url, JSON.toJSON(param));

        OkHttpClient client = OkHttpUtils.getOkHttpClient();
        Request request = OkHttpUtils.getJsonPostRequest(url, param);
        Call call = client.newCall(request);

        seeExecutor.execute(() -> {
            try (Response response = call.execute()) {
                StringBuilder builder = new StringBuilder();

                // 判断模型接口是否调用成功
                if (!response.isSuccessful()) {
                    handleErrorResponse(emitter, "Unexpected code " + response);
                    return;
                }

                ResponseBody body = response.body();
                CharsetDecoder decoder = Charset.forName("UTF-8").newDecoder();
                decoder.onMalformedInput(CodingErrorAction.IGNORE); // 忽略错误，也可以选择报告或替换

                try (InputStream inputStream = Objects.requireNonNull(body).byteStream();
                     Reader reader = new InputStreamReader(inputStream, decoder)) {

                    char[] buffer = new char[1024];
                    int charsRead;

                    while ((charsRead = reader.read(buffer)) != -1) {
                        String chunk = new String(buffer, 0, charsRead);
                        emitter.send(chunk, MediaType.TEXT_PLAIN);
                        builder.append(chunk);

                        // 在答案长度达到20时，每增加3个字符检查一次
                        if (builder.length() >= 20 && builder.length() % 3 == 0) {
                            if (checkSpam(builder.toString())) {
                                handleSpamResponse(emitter, WARN_ANSWER);
                                chatVO.setAnswer(WARN_ANSWER);
                                handleCompletedResponse(emitter,chatVO);
                                emitter.complete();
                                saveQuestionAndAnswerAndSession(chatVO,builder.toString());
                                return;
                            }
                        }
                    }
                }

                // 数据接收完成，进行安全性校验
                if(checkSpam(builder.toString())){
                    handleSpamResponse(emitter,WARN_ANSWER);
                    chatVO.setAnswer(WARN_ANSWER);
                    handleCompletedResponse(emitter,chatVO);
                    saveQuestionAndAnswerAndSession(chatVO,builder.toString());
                    emitter.complete();
                    return;
                }
                // 结果存储
                chatVO.setAnswer(builder.toString());
                saveQuestionAndAnswerAndSession(chatVO,StrUtil.EMPTY);
                handleCompletedResponse(emitter,chatVO);
                emitter.complete();
            } catch (IOException e) {
                log.error("与模型对话出错！",e);
                handleException(emitter, e.getMessage());
            }
        });

        return emitter;
    }

    private List<String[]> getHistory(Long sessionId){
        List<String[]> history = new ArrayList<>();
        List<ChatQuestionModel> questionModelList = chatQuestionRepository.findBySessionId(sessionId);
        if(questionModelList.size() > 0){
            List<ChatAnswerModel> answerModelList = chatAnswerRepository.findBySessionId(sessionId);
            Iterator<ChatAnswerModel> iterator = answerModelList.iterator();
            Map<Long,String> chatAnswerMap = new HashMap<>();
            while (iterator.hasNext()){
                ChatAnswerModel chatAnswerModel = iterator.next();
                chatAnswerMap.put(chatAnswerModel.getQuestionId(),chatAnswerModel.getContent());
            }
            Iterator<ChatQuestionModel> questionModelIterator = questionModelList.iterator();
            while (questionModelIterator.hasNext()){
                ChatQuestionModel questionModel = questionModelIterator.next();
                history.add(new String[]{questionModel.getContent(),chatAnswerMap.get(questionModel.getId())});
            }
        }

        return history;
    }

    private void updateAnswerThumbs(Long id,int thumbs){
        Optional<ChatAnswerModel> optional = chatAnswerRepository.findById(id);
        if(!optional.isPresent()){
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED,"回答不存在！");
        }
        ChatAnswerModel chatAnswerModel = optional.get();
        chatAnswerModel.setMark(thumbs);
        chatAnswerRepository.save(chatAnswerModel);
    }

    private void handleErrorResponse(SseEmitter emitter, String errorMessage) {
        try {
            emitter.send(SseEmitter.event().name("error").data(errorMessage));
            emitter.complete();
        } catch (IOException e) {
            log.error("模型对话出错！",e);
            handleException(emitter, e.getMessage());
        }
    }

    private void handleException(SseEmitter emitter, String errorMessage) {
        try {
            emitter.send(SseEmitter.event().name("error").data(errorMessage));
            emitter.complete();
        } catch (ClientAbortException clientAbortException) {
            log.error("连接关闭失败！",clientAbortException);
        } catch (IOException e) {
            log.error("处理异常", e);
        }
    }

    private boolean checkSpam(String content) {
        if(StringUtils.isEmpty(content)){
            return false;
        }
        Boolean antispam = antispamUtil.checkAntispam(snowflake.nextIdStr(), content);
        return (antispam != null && antispam);
    }

    private void handleSpamResponse(SseEmitter emitter,String message) {
        try {
            emitter.send(SseEmitter.event().name("spam")
                    .data(message));
        } catch (IOException e) {
            log.error("模型对话出错！", e);
            handleException(emitter, e.getMessage());
        }
    }
    private void handleCompletedResponse(SseEmitter emitter,ChatVO chatVO) {
        try {
            emitter.send(SseEmitter.event().name("Completed")
                    .data(chatVO));
        } catch (IOException e) {
            log.error("模型对话出错！", e);
            handleException(emitter, e.getMessage());
        }
    }

    /**
     * 保存对话内容并生成历史session
     * @param chatVO
     * @param rawAnswer
     */
    private void saveQuestionAndAnswerAndSession(ChatVO chatVO, String rawAnswer){
        log.info("模型回答："+chatVO.getAnswer());
        ChatQuestionModel chatQuestionModel = new ChatQuestionModel();
        chatQuestionModel.setId(chatVO.getQuestionId());
        chatQuestionModel.setContent(chatVO.getQuestion());
        chatQuestionModel.setSessionId(chatVO.getSessionId());
        chatQuestionModel.setCtime(new Date());
        chatQuestionRepository.save(chatQuestionModel);
        ChatAnswerModel chatAnswerModel;
        chatAnswerModel = new ChatAnswerModel();
        chatAnswerModel.setId(chatVO.getAnswerId());
        chatAnswerModel.setMark(0);
        chatAnswerModel.setSource(0);
        chatAnswerModel.setCtime(new Date());
        chatAnswerModel.setContent(chatVO.getAnswer());
        chatAnswerModel.setQuestionId(chatVO.getQuestionId());
        chatAnswerModel.setSessionId(chatVO.getSessionId());
        chatAnswerModel.setRawAnswer(rawAnswer);
        chatAnswerRepository.save(chatAnswerModel);
        Optional<ChatSessionModel> optional = chatSessionRepository.findById(chatVO.getSessionId());
        if(!optional.isPresent()){
            ChatSessionModel chatSessionModel = new ChatSessionModel();
            chatSessionModel.setId(chatVO.getSessionId());
            chatSessionModel.setCtime(new Date());
            chatSessionRepository.save(chatSessionModel);
        }
//        todo：是否要记录第三方接入信息？目前记录的是从南院还是北院进行的挂号
    }
}
