package com.center.huatuo.chat.registration;

import com.center.framework.common.exception.ServerException;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.db.config.SnowFlakeConfig;
import com.center.framework.web.pojo.CommonResult;
import com.center.huatuo.chat.registration.pojo.TextToSpeechReq;
import com.center.huatuo.common.utils.TencentUtils;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.tts.v20190823.TtsClient;
import com.tencentcloudapi.tts.v20190823.models.TextToVoiceRequest;
import com.tencentcloudapi.tts.v20190823.models.TextToVoiceResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Tag(name = "获取腾讯语音服务")
@RequestMapping("/chat")
@Slf4j
public class GetTokenController {

    @Resource
    private SnowFlakeConfig snowFlakeConfig;

    @Operation(summary = "获取腾讯语音服务(语音转文字)Credentials")
    @GetMapping (value = "/getTsc")
    public CommonResult<String> getAsrCredentials() {
        return CommonResult.success(TencentUtils.GetTmpCredentials("asr"));
    }
    @Operation(summary = "获取腾讯语音服务(文字转语音)Credentials")
    @GetMapping (value = "/getTts")
    public CommonResult<String> getTtsCredentials() {
        return CommonResult.success(TencentUtils.GetTmpCredentials("tts"));
    }

  @Operation(summary = "获取腾讯语音服务-文字转语音")
  @PostMapping(value = "/text_to_speech")
  public CommonResult<String> textToSpeech(@RequestBody TextToSpeechReq textToSpeechReq) {
    Credential cred =
        new Credential("AKIDwteCY9ohov7eNMJD06LnDjefWVj5VTTT", "5DKWmzKjM5ttLMQVj4bNg9Xr6kHtTAlM");
    // 实例化一个http选项，可选的，没有特殊需求可以跳过
    HttpProfile httpProfile = new HttpProfile();
    httpProfile.setEndpoint("tts.tencentcloudapi.com");
    // 实例化一个client选项，可选的，没有特殊需求可以跳过
    ClientProfile clientProfile = new ClientProfile();
    clientProfile.setHttpProfile(httpProfile);
    // 实例化要请求产品的client对象,clientProfile是可选的
    TtsClient client = new TtsClient(cred, "", clientProfile);
    // 实例化一个请求对象,每个接口都会对应一个request对象
    TextToVoiceRequest req = new TextToVoiceRequest();
    if(textToSpeechReq.getText().length()>150){
        req.setText(textToSpeechReq.getText().substring(0,150));
    }else{
        req.setText(textToSpeechReq.getText());
    }
      req.setSessionId(snowFlakeConfig.snowFlakeCore().nextIdStr());
      req.setVolume(5F);
      req.setSpeed(0F);
      req.setVoiceType(501004L);
      req.setSampleRate(16000L);
      req.setCodec("mp3");
    // 返回的resp是一个TextToVoiceResponse的实例，与请求对象对应
    try {
      TextToVoiceResponse resp = client.TextToVoice(req);
      return CommonResult.success(resp.getAudio());
    } catch (TencentCloudSDKException e) {
        log.error("文件转语音出错！",e);
        throw new ServerException(
                GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(),"系统正忙，请稍后再试",e.getMessage());    }
  }
}
