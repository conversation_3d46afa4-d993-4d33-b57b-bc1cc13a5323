package com.center.huatuo.chat.consultation.cacheHelper;

import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class CacheHelper {
    private static final Map<String, String> QUESTION_CATEGORY_MAP = new HashMap<>();

    static {
        // 初始化分类映射
        String[] painPositionQuestions = {"剑突下", "右上腹", "右侧腹","中腹（脐区）","右下腹","下腹正中","上腹正中","左上腹","左侧腹","左下腹"};
        String[] timeQuestions = {"一周", "一个月", "三个月","半年以上", "以上均没有"};
        String[] painTypeQuestions = {"钝痛","隐痛","胀痛","绞痛", "以上均没有"};
        String[] painDurationQuestions = {"持续性的","间歇性的", "以上均没有"};
        String[] pastMedicalHistoryQuestions = {"高血压","糖尿病","心脑血管疾病", "以上均没有", "其他"};
        String[] allergyHistoryQuestions = {"青霉素过敏","头孢过敏","海鲜过敏", "以上均没有", "其他"};
        String[] familyMedicalHistoryQuestions = {"色盲","近视", "以上均没有", "其他"};
        
        // 新的特殊分类
        String[] specialOptions = {"以上均没有", "其他"};

        for (String question : painPositionQuestions) {
            QUESTION_CATEGORY_MAP.put(question, "painPosition");
        }
        for (String question : timeQuestions) {
            QUESTION_CATEGORY_MAP.put(question, "time");
        }
        for (String question : painTypeQuestions) {
            QUESTION_CATEGORY_MAP.put(question, "painType");
        }
        for (String question : painDurationQuestions) {
            QUESTION_CATEGORY_MAP.put(question, "painDuration");
        }
        for (String question : pastMedicalHistoryQuestions) {
            QUESTION_CATEGORY_MAP.put(question, "pastMedicalHistory");
        }
        for (String question : allergyHistoryQuestions) {
            QUESTION_CATEGORY_MAP.put(question, "allergyHistory");
        }
        for (String question : familyMedicalHistoryQuestions) {
            QUESTION_CATEGORY_MAP.put(question, "familyMedicalHistory");
        }
        
        // 添加特殊选项映射
        QUESTION_CATEGORY_MAP.put("以上均没有", "noneOption");
        QUESTION_CATEGORY_MAP.put("其他", "otherOption");
    }

    public static Set<String> getCacheCategories(String question) {
        // 现有代码已经能够正确处理从前端接收到的"以上均没有"和自定义内容的选项
        // 会正确识别 "以上均没有,鼻炎" 这样的格式，并提取出自定义内容
        Set<String> categories = new HashSet<>();
        String[] words = question.split("[,，]"); // 按逗号或中文逗号分割
        
        System.out.println("处理问题: " + question);
        System.out.println("分割后的词: " + Arrays.toString(words));
        
        // 直接检查原始问题是否包含某些类别的关键词
        if (matchesTimeCategory(question)) {
            categories.add("time");
            System.out.println("匹配到time类别");
        }
        if (matchesPainPositionCategory(question)) {
            categories.add("painPosition");
            System.out.println("匹配到painPosition类别");
        }
        if (matchesPainTypeCategory(question)) {
            categories.add("painType");
            System.out.println("匹配到painType类别");
        }
        if (matchesPainDurationCategory(question)) {
            categories.add("painDuration");
            System.out.println("匹配到painDuration类别");
        }
        
        boolean hasNoneOption = false;
        String customContent = null;
        
        // 首先检查是否包含"以上均没有"选项
        for (String word : words) {
            String trimmedWord = word.trim();
            if ("以上均没有".equals(trimmedWord)) {
                hasNoneOption = true;
                categories.add("noneOption");
                System.out.println("检测到'以上均没有'选项");
                break;
            }
        }
        
        // 如果包含"以上均没有"，并且数组长度大于1，那么其他部分可能是自定义内容
        if (hasNoneOption && words.length > 1) {
            // 从第二个元素开始，收集所有非"以上均没有"的内容作为自定义内容
            StringBuilder customContentBuilder = new StringBuilder();
            for (String word : words) {
                String trimmedWord = word.trim();
                if (!"以上均没有".equals(trimmedWord) && StringUtils.isNotBlank(trimmedWord)) {
                    if (customContentBuilder.length() > 0) {
                        customContentBuilder.append(", ");
                    }
                    customContentBuilder.append(trimmedWord);
                }
            }
            
            if (customContentBuilder.length() > 0) {
                customContent = customContentBuilder.toString();
                categories.add("otherOption");
                categories.add("customContent:" + customContent);
                System.out.println("提取到自定义内容: " + customContent);
            }
        } else {
            // 正常处理每个选项
            for (String word : words) {
                String trimmedWord = word.trim();
                String category = QUESTION_CATEGORY_MAP.get(trimmedWord);
                if (StringUtils.isNotBlank(category)) {
                    categories.add(category);
                    System.out.println("匹配到类别: " + category + " 对应选项: " + trimmedWord);
                }
                
                // 处理其他选项的自定义内容
                if (trimmedWord.startsWith("其他：") || trimmedWord.startsWith("其他:")) {
                    String content = trimmedWord.substring(3).trim();
                    if (StringUtils.isNotBlank(content)) {
                        // 添加原始选项值到结果集，以便能区分是哪类问题的"其他"选项
                        categories.add("otherOption");
                        // 将自定义内容添加到特殊字段
                        categories.add("customContent:" + content);
                        System.out.println("提取到'其他'自定义内容: " + content);
                    }
                }
            }
        }
        
        System.out.println("最终分类结果: " + categories);
        return categories.isEmpty() ? Collections.singleton("unknown") : categories;
    }
    
    // 以下是辅助方法，用于匹配不同类别的问题
    private static boolean matchesTimeCategory(String question) {
        for (String key : new String[]{"一周", "一个月", "三个月", "半年以上"}) {
            if (question.contains(key)) {
                return true;
            }
        }
        return false;
    }
    
    private static boolean matchesPainPositionCategory(String question) {
        for (String key : new String[]{"剑突下", "右上腹", "右侧腹", "中腹", "脐区", "右下腹", "下腹正中", "上腹正中", "左上腹", "左侧腹", "左下腹"}) {
            if (question.contains(key)) {
                return true;
            }
        }
        return false;
    }
    
    private static boolean matchesPainTypeCategory(String question) {
        for (String key : new String[]{"钝痛", "隐痛", "胀痛", "绞痛"}) {
            if (question.contains(key)) {
                return true;
            }
        }
        return false;
    }
    
    private static boolean matchesPainDurationCategory(String question) {
        for (String key : new String[]{"持续性", "间歇性"}) {
            if (question.contains(key)) {
                return true;
            }
        }
        return false;
    }
}

