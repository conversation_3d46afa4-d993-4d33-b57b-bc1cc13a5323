package com.center.huatuo.chat.consultation.persistence;

import com.center.framework.db.annotation.GenerateId;
import com.center.framework.db.core.BaseModel;
import lombok.Data;
import com.center.framework.db.listener.IgnoreNullEventListener;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

/**
 * @Date: 2023/3/13 19:51
 * @Description: 对话的回答
 */
@Data
@Table(name = "huatuo_consultation_chat_answer")
@Entity
@EntityListeners({AuditingEntityListener.class, IgnoreNullEventListener.class})
public class ConsultationChatAnswerModel extends BaseModel {


    /**
     * 回答内容
     */

    @Column(name = "content")
    private String content;
    /**
     * 回答来源
     */
    @Column(name = "source")
    private int source;
    /**
     * 评分，-1 踩，0 默认，1 赞
     */
    @Column(name = "mark")
    private int mark;
    /**
     * 问题id
     */
    @Column(name = "question_id")
    private Long questionId;
    /**
     * 对话id
     */
    @Column(name = "session_id")
    private Long sessionId;
    /**
     * 未进行后处理的answer
     */
    @Column(name = "raw_answer")
    private String rawAnswer;
    /**
     * 敏感词
     */
    @Column(name = "sensitive_words")
    private String sensitiveWords;

}
