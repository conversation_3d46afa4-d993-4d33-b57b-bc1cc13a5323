package com.center.huatuo.chat.consultation;

import com.center.framework.web.pojo.CommonResult;
import com.center.huatuo.chat.consultation.pojo.ConsultationChatHistoryResp;
import com.center.huatuo.chat.consultation.pojo.ConsultationChatReq;
import com.center.huatuo.chat.consultation.pojo.MockReportReq;
import com.center.huatuo.chat.consultation.service.ConsultationChatService;
import com.center.huatuo.report.pojo.ReportResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@Tag(name = "智能问诊")
@RequestMapping("/consultation_chat") // 类级别的路径前缀
@Validated  // 启用请求参数的验证
@Slf4j
public class ConsultationChatController {


    @Resource
    private ConsultationChatService consultationChatService;
    /**
     * 获取对话初始化的 sessionID
     */
    @GetMapping("/reconsult_init_session")
    @Operation(summary = "重新问诊获取问诊对话sessionID")
    public CommonResult<Long> initSession(@RequestParam Long appointmentId) {
        // 调用服务层生成新的 sessionId 并更新挂号信息
        Long sessionId = consultationChatService.initSession(appointmentId);
        return CommonResult.success(sessionId);
    }

    @PostMapping(value = "/consultationChat_with_model", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "进行问诊")
    public SseEmitter chatWithRobot(@RequestBody @Valid ConsultationChatReq consultationChatReq){
        return consultationChatService.consultationChat(consultationChatReq);
    }

    /**
     * 查询挂号ID的全部历史记录（最多200条）
     * @param appointmentId 挂号ID
     * @return 返回问诊历史记录
     */
    @GetMapping("/appointment_history/{appointmentId}")
    @Operation(summary = "查询挂号ID的全部历史记录")
    public CommonResult<List<ConsultationChatHistoryResp>> searchHistoryByAppointmentId(
            @PathVariable("appointmentId") Long appointmentId) {
        return CommonResult.success(consultationChatService.searchHistoryByAppointmentId(appointmentId));
    }

    /**
     * 查询挂号ID的全部历史记录（最多200条）
     * @param sessionId 挂号ID
     * @return 返回问诊历史记录
     */
    @GetMapping("/session_history/{sessionId}")
    @Operation(summary = "查询一次对话ID的历史记录，已经按照时间排列好")
    public CommonResult<List<ConsultationChatHistoryResp>> searchHistoryBySessionId(
            @PathVariable("sessionId") Long sessionId) {
        return CommonResult.success(consultationChatService.searchHistoryBySessionId(sessionId));
    }
    @PostMapping("/generateReport/{appointmentId}")
    @Operation(summary = "生成问诊报告,返回的是已经生成的问诊报告id")
    public CommonResult<Long> generateConsultationReport(@Valid @PathVariable Long appointmentId) {
        return CommonResult.success(consultationChatService.generateConsultationReport(appointmentId));
    }

}
