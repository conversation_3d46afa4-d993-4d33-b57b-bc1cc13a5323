package com.center.huatuo.chat.consultation.service;


import com.center.huatuo.chat.consultation.pojo.ConsultationChatHistoryResp;
import com.center.huatuo.chat.consultation.pojo.ConsultationChatReq;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import java.util.List;

public interface ConsultationChatService {

    /**
     * 初始化会话，生成 sessionId，并更新挂号信息中的最新 sessionId
     * @param appointmentId 挂号ID
     * @return 生成的 sessionId
     */
    Long initSession(Long appointmentId);

    /**
     * 开启一个问诊聊天会话
     *
     * 此方法用于处理问诊聊天请求，通过SseEmitter实现服务端与客户端之间的长连接，
     * 以便实时推送消息给客户端这种连接方式常用于实现服务器向客户端推送实时更新，
     * 包含生成问诊报告
     *
     * @param consultationChatReq 问诊聊天请求对象，包含聊天会话的相关信息和参数
     * @return 返回一个SseEmitter对象，用于向客户端推送聊天消息
     */
    SseEmitter consultationChat(ConsultationChatReq consultationChatReq);


    /**
     * 根据挂号ID搜索历史咨询聊天记录
     *
     * @param appointmentId 挂号ID，用于标识特定的预约记录
     * @return 返回一个列表，包含所有与指定预约ID相关的咨询聊天历史记录
     */
    List<ConsultationChatHistoryResp> searchHistoryByAppointmentId(Long appointmentId);

    /**
     * 根据会话ID搜索历史记录
     *
     * @param sessionId 会话ID，用于标识特定的会话
     * @return 返回一个ConsultationChatHistoryResp对象的列表，包含会话的历史记录
     */
    List<ConsultationChatHistoryResp> searchHistoryBySessionId(Long sessionId);

    Long generateConsultationReport(Long appointmentId);


}
