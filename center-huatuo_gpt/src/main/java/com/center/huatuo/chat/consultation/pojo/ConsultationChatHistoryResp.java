package com.center.huatuo.chat.consultation.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ConsultationChatHistoryResp {

    @Schema(description = "问题信息")
    private ConsultationChatQuestionBase consultationChatQuestionBase;  // 问题信息（可以包含ID、内容等）

    @Schema(description = "答案信息")
    private ConsultationChatAnswerBase consultationChatAnswerBase;  // 答案信息（可以包含ID、内容等）

    @Schema(description = "图片信息")
    private List<ConsultationChatImageBase> consultationChatImageBaseList;  // 图片信息（可以包含ID、图片路径等）

}
