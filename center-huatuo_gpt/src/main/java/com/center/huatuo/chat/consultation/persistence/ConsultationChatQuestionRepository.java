package com.center.huatuo.chat.consultation.persistence;


import com.center.framework.db.core.JoinFetchCapableQueryDslJpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ConsultationChatQuestionRepository extends JoinFetchCapableQueryDslJpaRepository<ConsultationChatQuestionModel, Long> {

    List<ConsultationChatQuestionModel> findBySessionId(Long sessionId);
}
