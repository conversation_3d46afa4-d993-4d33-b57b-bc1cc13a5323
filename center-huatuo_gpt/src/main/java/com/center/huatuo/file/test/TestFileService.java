package com.center.huatuo.file.test;


import com.center.framework.storage.factory.FileStorageServiceFactory;
import com.center.framework.storage.interfaces.FileStorageService;
import com.center.framework.storage.interfaces.pojo.FileListResp;
import com.center.framework.storage.interfaces.pojo.FileMetadata;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

@Service
public class TestFileService {

    private final FileStorageService fileStorageService;

    @Autowired
    public TestFileService(FileStorageServiceFactory fileStorageServiceFactory) {
        this.fileStorageService = fileStorageServiceFactory.getFileStorageService();
    }


    // 文件操作方法
    public String uploadFile(MultipartFile file,Boolean overwrite) throws IOException {
        String targetPath = "uploads/TEST/" + file.getOriginalFilename();
        try (InputStream inputStream = file.getInputStream()) {
            fileStorageService.uploadFile(targetPath, inputStream,overwrite);
        }
        return targetPath;
    }

    public InputStream downloadFile(String filePath) throws IOException {
        return fileStorageService.downloadFile(filePath);
    }

    public void deleteFile(String filePath) throws IOException {
        fileStorageService.deleteFile(filePath);
    }

    public FileMetadata getFileMetadata(String filePath) throws IOException {
        return fileStorageService.getFileMetadata(filePath);
    }

    ResponseEntity<ByteArrayResource> previewFile(String filePath) throws IOException{
        return fileStorageService.previewFile(filePath);
    }

    public String convertPdf(String originPath){
        return fileStorageService.convertPdf(originPath);
    }
    /**
     * 获取文件目录下的文件列表
     * @param path 文件夹路径
     * @return 文件列表
     * @throws IOException 文件操作异常
     */
    public List<FileListResp> getFileList(String path) throws IOException {
        // 判断是 MinIO 还是 HDFS，根据文件路径或者其他方式选择合适的存储方式
        return fileStorageService.getAllFiles(path);
    }
}
