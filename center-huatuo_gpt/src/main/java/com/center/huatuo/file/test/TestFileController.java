package com.center.huatuo.file.test;

import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.storage.interfaces.pojo.FileListResp;
import com.center.framework.storage.interfaces.pojo.FileMetadata;
import com.center.framework.web.pojo.CommonResult;
import com.netease.yidun.sdk.core.utils.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

@RestController
@Tag(name = "文件存储测试类")
@RequestMapping("/api/test/files")
@Slf4j
public class TestFileController {

    @Autowired
    private TestFileService testFileService;

    @PostMapping("/upload")
    @Operation(summary = "上传文件", description = "上传文件并存储到指定路径")
    public CommonResult<String> uploadFile(@RequestParam("file") MultipartFile file,@RequestParam Boolean overwrite) throws Exception {

        String path = testFileService.uploadFile(file, overwrite);
        return CommonResult.success("文件上传成功",path);
    }


    @GetMapping("/download")
    @Operation(summary = "下载文件", description = "根据文件路径下载文件")
    public ResponseEntity<InputStreamResource> downloadFile(@RequestParam("filePath") String filePath) {
        try {
            // 获取文件流
            InputStream inputStream = testFileService.downloadFile(filePath);

            // 获取文件名
            String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);

            // 对文件名进行编码
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");

            // 创建响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + encodedFileName + "\"; filename*=UTF-8''" + encodedFileName);
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

            // 返回流
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(new InputStreamResource(inputStream));
        } catch (IOException e) {
            log.error("文件下载失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件下载失败：" + e.getMessage());
        }
    }


    @DeleteMapping("/delete")
    @Operation(summary = "删除文件", description = "根据文件路径删除文件")
    public CommonResult<String> deleteFile(@RequestParam("filePath") String filePath) {
        try {
            testFileService.deleteFile(filePath);
            return CommonResult.successWithMessageOnly("文件删除成功");
        } catch (IOException e) {
            log.error("文件删除失败", e);
            return CommonResult.error(GlobalErrorCodeConstants.DELETE_OBJECT_ERROR.getCode(), "文件删除失败：" + e.getMessage());
        }
    }

    @GetMapping("/metadata")
    @Operation(summary = "获取文件元数据", description = "根据文件路径获取文件的元数据")
    public CommonResult<FileMetadata> getFileMetadata(@RequestParam("filePath") String filePath) {
        try {
            FileMetadata fileMetadata = testFileService.getFileMetadata(filePath);
            return CommonResult.success(fileMetadata);
        } catch (IOException e) {
            log.error("获取文件元数据失败", e);
            return CommonResult.error(GlobalErrorCodeConstants.FILE_NOT_FOUND.getCode(), "获取文件元数据失败：" + e.getMessage());
        }
    }

    /**
     * 文件预览接口
     * @param filePath 文件路径
     * @return 文件数据
     */
    @GetMapping("/preview")
    @Operation(summary = "获取预览文件")
    public ResponseEntity<ByteArrayResource> previewFile(@RequestParam String filePath) {
        if (StringUtils.isBlank(filePath)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILE_NOT_FOUND, "文件路径");
        }
        try {
            return testFileService.previewFile(filePath);
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @GetMapping("/convert")
    @Operation(summary = "转换pdf")
    public CommonResult<String> convert(@RequestParam String filePath) {
        return CommonResult.success(testFileService.convertPdf(filePath));
    }
    /**
     * 获取文件目录下的文件列表
     * @param path 文件夹路径
     * @return 文件列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取文件目录", description = "根据文件夹路径获取文件夹下的所有文件")
    public CommonResult<List<FileListResp>> getFileList(@RequestParam("path") String path) {
        try {
            // 调用服务层获取目录下的文件列表
            List<FileListResp> fileList = testFileService.getFileList(path);
            return CommonResult.success(fileList);
        } catch (IOException e) {
            log.error("获取文件列表失败", e);
            return CommonResult.error(GlobalErrorCodeConstants.FILE_NOT_FOUND.getCode(), "获取文件列表失败：" + e.getMessage());
        }
    }

}
