package com.center.huatuo.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

import java.util.Random;

@AllArgsConstructor
public enum HuatuoAppointmentsRegistrationTypeEnum implements IEnumerate<String> {
    EXPERT("EXPERT", "专家"),
    GENERAL("GENERAL", "普通");

    private String value;
    private String description;
    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    //随机生成号别
    private static final Random RANDOM = new Random();
    private static final HuatuoAppointmentsRegistrationTypeEnum[] VALUES = values();

    public static HuatuoAppointmentsRegistrationTypeEnum getRandom() {
        return VALUES[RANDOM.nextInt(VALUES.length)];
    }
}
