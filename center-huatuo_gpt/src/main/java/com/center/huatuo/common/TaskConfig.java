package com.center.huatuo.common;

import com.alibaba.ttl.threadpool.TtlExecutors;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableScheduling
@EnableAsync
public class TaskConfig {

    /**
     * 定时任务专用线程池（如原代码所示）
     */
    @Bean(name = "scheduledTaskExecutor")
    public ThreadPoolTaskExecutor scheduledTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5); // 核心线程数
        executor.setMaxPoolSize(20); // 最大线程数
        executor.setQueueCapacity(50); // 队列容量
        executor.setThreadNamePrefix("scheduled-task-");
        executor.setKeepAliveSeconds(60); // 非核心线程的空闲时间
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy()); // 拒绝策略
        executor.initialize();
        return executor;
    }

    /**
     * 异步任务线程池
     * 使用 TtlExecutors 对线程池进行包装，支持TransmittableThreadLocal上下文传递
     */
    @Bean(name = "seeExecutor")
    @Primary
    public Executor seeExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10); // 核心线程数，可根据业务需求调整
        executor.setMaxPoolSize(20); // 最大线程数，根据业务需求调整
        executor.setQueueCapacity(100); // 队列容量，根据业务需求调整
        executor.setThreadNamePrefix("see-executor-");
        executor.setKeepAliveSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();

        // 使用 TtlExecutors 包装线程池
        return TtlExecutors.getTtlExecutor(executor.getThreadPoolExecutor());
    }
}
