package com.center.huatuo.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum HuatuoAppointmentStatusEnum implements IEnumerate<String> {
    UNATTENDED("UNATTENDED", "未就诊"),
    ATTENDED("ATTENDED", "已就诊"),
    CANCELED("CANCELED", "已取消");


    private String value;
    private String description;
    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
