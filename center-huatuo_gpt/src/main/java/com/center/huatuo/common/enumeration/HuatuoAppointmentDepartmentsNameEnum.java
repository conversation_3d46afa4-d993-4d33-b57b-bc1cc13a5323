package com.center.huatuo.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

import java.util.Random;

@AllArgsConstructor
public enum HuatuoAppointmentDepartmentsNameEnum implements IEnumerate<String> {
    INTERNALMEDICAL("INTERNALMEDICAL", "内科"),
    SURGERY("SURGERY", "外科"),
    GYNAECOLOGYANDOBSTETRICS("GYNAECOLOGYANDOBSTETRICS", "妇产科"),
    PADIATRIC("PADIATRIC", "儿科"),
    ONCOLOGY("ONCOLOGY", "肿瘤科"),;

    private String value;
    private String description;
    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }


    private static final Random RANDOM = new Random();
    private static final HuatuoAppointmentDepartmentsNameEnum[] VALUES = values();

    public static HuatuoAppointmentDepartmentsNameEnum getRandom() {
        return VALUES[RANDOM.nextInt(VALUES.length)];
    }
}
