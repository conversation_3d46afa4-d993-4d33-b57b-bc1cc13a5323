package com.center.huatuo.common.enumeration;

import com.center.framework.common.enumerate.IEnumerate;
import lombok.AllArgsConstructor;

import java.util.Random;

@AllArgsConstructor
public enum HuatuoAppointmentDoctorLevelEnum implements IEnumerate<String> {
    PHYSICIAN("PHYSICIAN", "医师"),
    DEPUTYCHIEFPHYSICIAN("DEPUTYCHIEFPHYSICIAN", "副主任医师"),
    CHIEFPHYSICIAN("CHIEFPHYSICIAN", "主任医师"),;

    private String value;
    private String description;
    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    private static final Random RANDOM = new Random();

    public static HuatuoAppointmentDoctorLevelEnum getRandom() {
        HuatuoAppointmentDoctorLevelEnum[] levels = {
                DEPUTYCHIEFPHYSICIAN,
                CHIEFPHYSICIAN
        };

        return levels[RANDOM.nextInt(levels.length)];
    }
}
