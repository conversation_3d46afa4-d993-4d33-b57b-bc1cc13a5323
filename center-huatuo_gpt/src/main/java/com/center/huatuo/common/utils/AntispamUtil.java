package com.center.huatuo.common.utils;

import com.netease.yidun.sdk.antispam.AntispamRequester;
import com.netease.yidun.sdk.antispam.text.TextClient;
import com.netease.yidun.sdk.antispam.text.v5.check.sync.single.TextCheckRequest;
import com.netease.yidun.sdk.antispam.text.v5.check.sync.single.TextCheckResponse;
import com.netease.yidun.sdk.antispam.text.v5.check.sync.single.TextCheckResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 */
@Slf4j
@Service
public class AntispamUtil {

//    @Value("${antispam.secret.id:c5c88a23fb949a2a1f515b9bbfb0ca4e}")  //之前深圳研究院用的
    @Value("${antispam.secret.id:a2526d72ab8d8e8573e9647f1025db04}")
    private String secretId;
//    @Value("${antispam.secret.key:f83f63aaa66233d61cfe9a9975b2891f}") //之前深圳研究院用的
    @Value("${antispam.secret.key:bf2b4c0bff95fa3fdda09e773115f345}")
    private String secretKey;
//    @Value("${antispam.business.id:70378028d39b167fd136f5f02b936010}") //之前深圳研究院用的
    @Value("${antispam.business.id:8e177c648a26d02fc4d0a6578a21743e}")
    private String businessId;
    @Value("${antispam.region.code:cn-hangzhou}")
    private String regionCode;


    private AntispamRequester antispamRequester;
    private TextClient textClient;

    private void init() {
        if (antispamRequester == null || textClient == null) {
            // 实例化一个requester，入参需要传入易盾内容安全分配的secretId，secretKey
            this.antispamRequester = createAntispamRequester(secretId, secretKey);
            // 实例化发起请求的client对象
            this.textClient = TextClient.getInstance(antispamRequester);
        }
    }

    /**
     * 检查数据是否涉及敏感信息
     *
     * @param dataId
     * @param content
     * @return
     */
    public Boolean checkAntispam(String dataId, String content) {
        init();
        // 实例化请求对象
        TextCheckRequest checkRequest = new TextCheckRequest();
        // 设置易盾内容安全分配的businessId
        checkRequest.setBusinessId(businessId);

        // 根据需要设置请求的检测节点，默认杭州，现阶段只支持检测接口
        checkRequest.setRegionCode(regionCode);

        // 设置检测内容
        // 数据唯一标识
        checkRequest.setDataId(dataId);
        // 检测内容
        checkRequest.setContent(content);

        // 请求对象中的其他参数如果有需要，请参考官方接口文档中字段说明，按需添加
        TextCheckResponse checkResponse = null;
        try {
            // 发起同步检测的请求
            checkResponse = textClient.syncCheckText(checkRequest);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (checkResponse != null && checkResponse.getCode() == 200) {
            // 获取文本的检测结果，具体返回字段的说明，请参考官方接口文档中字段说明
            TextCheckResult textResult = checkResponse.getResult();
            TextCheckResult.Antispam antispam = textResult.getAntispam();
            List<TextCheckResult.AntispamLabel> labels = antispam.getLabels();
            if (null == labels || labels.isEmpty()) {
                return Boolean.FALSE;
            } else {
                for (TextCheckResult.AntispamLabel label : labels) {
                    if (label.getLevel().intValue() == 2) {
                        return Boolean.TRUE;
                    } else if (label.getLevel().intValue() == 1) {
                        // 警告
                    }
                }
            }
        }
        return Boolean.FALSE;
    }


    /**
     * 创建内容安全通用请求器
     *
     * @param secretId  产品密钥ID
     * @param secretKey 产品私有密钥
     * @return 内容安全请求器
     */
    private AntispamRequester createAntispamRequester(String secretId, String secretKey) {
        // 实例化一个requester，入参需要传入易盾内容安全分配的secretId，secretKey
        AntispamRequester antispamRequester = new AntispamRequester(secretId, secretKey);

        // 可选自定义请求器的参数，如果不需要自定义设置，可跳过，否则请参考如下注释内容：
        // ClientProfile clientProfile = createProfile("SecretId", "SecretKey");

        return antispamRequester;
    }

}
