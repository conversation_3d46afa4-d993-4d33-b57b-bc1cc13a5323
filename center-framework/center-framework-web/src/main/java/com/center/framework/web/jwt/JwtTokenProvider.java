package com.center.framework.web.jwt;

import cn.hutool.core.util.StrUtil;
import com.center.framework.web.constants.JwtCommonKeys;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.nio.charset.Charset;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class JwtTokenProvider {

  @Value("${center.jwt.secret-key}")
  private String secretKey;

  @Value("${center.jwt.expire-in}")
  private long validityInMilliseconds;

  @Value("${center.jwt.default-checksum}")
  private String defaultCheckSum;


  private final String TOKEN_KEY = "Authorization";

  private final String TOKEN_PREFIX = "Bearer ";

  private final String CHECKSUM = "checksum";

  @PostConstruct
  protected void init() {
    secretKey = Base64.getEncoder().encodeToString(secretKey.getBytes(Charset.forName("UTF-8")));
  }


  /**
   * 创建token.
   *
   * @param userId userId
   * @param tenantId tenantId
   * @return token
   */
  public String createToken(Long userId, Long tenantId) {
    return createToken(userId,tenantId,null,defaultCheckSum);
  }
  /**
   * 创建token.
   *
   * @param userId userId
   * @param tenantId tenantId
   * @return token
   */
  public String createToken(Long userId, Long tenantId,Long departId) {
    return createToken(userId,tenantId,departId,defaultCheckSum);
  }


  /**
   * 创建token
   * @param userId
   * @param tenantId
   * @param departId
   * @param checksum
   * @return
   */
  public String createToken(Long userId,Long tenantId, Long departId, String checksum) {
    Claims claims = Jwts.claims().setSubject(userId.toString());
    claims.put(JwtCommonKeys.USER_ID, userId);
    claims.put(JwtCommonKeys.DEPART_ID, departId);
    claims.put(JwtCommonKeys.TENANT_ID,tenantId);
    claims.put(CHECKSUM, checksum);
    Date now = new Date();
    Date validity = new Date(now.getTime() + validityInMilliseconds);

    return Jwts.builder()
        .setClaims(claims)
        .setIssuedAt(now)
        .setExpiration(validity)
        .signWith(SignatureAlgorithm.HS256, secretKey)
        .compact();
  }

  /**
   * get userId from token.
   *
   * @param token token
   * @return userId
   */
  public Long getUserId(String token) {
    Object userId = getClaimsFromToken(token)
        .get(JwtCommonKeys.USER_ID);
    return userId == null ? null : Long.valueOf(userId.toString());
  }

  public Long getTenantId(String token) {
    Object tenantId = getClaimsFromToken(token).get(JwtCommonKeys.TENANT_ID);
    return tenantId == null ? null : Long.valueOf(tenantId.toString());
  }

  public Long getDepartId(String token) {
    Object departId = getClaimsFromToken(token)
        .get(JwtCommonKeys.DEPART_ID);
    return departId == null ? null : Long.valueOf(departId.toString());
  }

  public String getSessionKey(String token) {
    Object sessionKey = getClaimsFromToken(token)
        .get(JwtCommonKeys.SESSION_KEY);
    return secretKey == null ? null : sessionKey.toString();
  }

  public String getAccessionToken(String token) {
    Object accessionToken = getClaimsFromToken(token)
        .get(JwtCommonKeys.ACCESS_TOKEN);
    return accessionToken == null ? null : accessionToken.toString();
  }

  /**
   * get checksum from token
   *
   * @return checksum
   */
  public String getChecksum(String token) {
    Object checksum = getClaimsFromToken(token)
        .get(CHECKSUM);
    return checksum == null ? null : checksum.toString();
  }

  /**
   * get actions from token.
   *
   * @param token token
   * @return userId
   */
  public List<String> getActions(String token) {
    Object actions = getClaimsFromToken(token)
        .get(JwtCommonKeys.ACTION_VALUES);
    return actions == null ? null : (List<String>) actions;
  }


  /**
   * get token string from http header map.
   *
   * @param req HttpServletRequest
   * @return token
   */
  public String resolveToken(HttpServletRequest req) {
    String bearerToken = req.getHeader(TOKEN_KEY);
    if (bearerToken != null && bearerToken.startsWith(TOKEN_PREFIX)) {
      return bearerToken.substring(7, bearerToken.length());
    }else if(req.getParameterMap().containsKey("optional")) {
//      临时支持前端页面图片通过src=url进行预览时，无法在header中添加token。改为在url参数中传递token
      return req.getParameter("optional");
    }
    return null;
  }

  /**
   * valid token.
   *
   * @param token token
   * @return boolean
   */
  public boolean validateToken(String token) {
    try {
      Jwts.parser().setSigningKey(secretKey).parseClaimsJws(token);
      return true;
    } catch (JwtException | IllegalArgumentException e) {
      log.error("验证token出错.(token={})",token,e);
      return false;
    }
  }

  private Claims getClaimsFromToken(String token) {
    Claims claims;
    try {
      claims = Jwts.parser()
          .setSigningKey(secretKey)
          .parseClaimsJws(token)
          .getBody();
    } catch (Exception e) {
      log.error("从Token中解析Claim出错.({})",token,e);
      claims = null;
    }
    return claims;
  }

  private String doGenerateToken(Claims claims) {
    Date now = new Date();
    Date validity = new Date(now.getTime() + validityInMilliseconds);
    return Jwts.builder()
        .setClaims(claims)
        .setIssuedAt(now)
        .setExpiration(validity)
        .signWith(SignatureAlgorithm.HS256, secretKey)
        .compact();
  }

  public String refreshToken(String token) {
    String refreshedToken;
    try {
      final Claims claims = getClaimsFromToken(token);
      refreshedToken = doGenerateToken(claims);
    } catch (Exception e) {
      log.error("生成refresh token出错.(token={})",token,e);
      refreshedToken = null;
    }
    return refreshedToken;
  }

}
