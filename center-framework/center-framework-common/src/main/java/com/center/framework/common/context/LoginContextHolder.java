package com.center.framework.common.context;

import com.alibaba.ttl.TransmittableThreadLocal;

import java.util.List;

public class LoginContextHolder {


  private static final ThreadLocal<Long> LOGIN_USER_ID = new TransmittableThreadLocal<>();
  private static final ThreadLocal<Long> LOGIN_USER_TENANT_ID = new TransmittableThreadLocal<>();

  private static final ThreadLocal<Long> LOGIN_USER_DEPART_ID = new TransmittableThreadLocal<>();

  /*添加登录账号的身份证字段*/
  private static final ThreadLocal<String> LOGIN_USER_IdentityNumber = new TransmittableThreadLocal<>();

  private static final ThreadLocal<List<Long>> LOGIN_USER_ROLE_IDS = new TransmittableThreadLocal<>();

  public static String getLoginUserIdentityNumber(){
    return LOGIN_USER_IdentityNumber.get();
  }
  public static void setLoginUserIdentityNumber(String IdentityNumber){
    LOGIN_USER_IdentityNumber.set(IdentityNumber);
  }

  public static Long getLoginUserId(){
    return LOGIN_USER_ID.get();
  }

  public static void setLoginUserId(Long id){
    LOGIN_USER_ID.set(id);
  }

  public static Long getLoginUserTenantId() {
    return LOGIN_USER_TENANT_ID.get();
  }

  public static void setLoginUserTenantId(Long userTenantId){
    LOGIN_USER_TENANT_ID.set(userTenantId);
  }

  public static Long getLoginUserDepartId(){
    return LOGIN_USER_DEPART_ID.get();
  }

  public static void setLoginUserDepartId(Long departId){
    LOGIN_USER_DEPART_ID.set(departId);
  }

  public static List<Long> getLoginUserRoleIds(){
    return LOGIN_USER_ROLE_IDS.get();
  }

  public static void setLoginUserRoleIds(List<Long> roleIds){
    LOGIN_USER_ROLE_IDS.set(roleIds);
  }
  public static void clear(){
    LOGIN_USER_TENANT_ID.remove();;
    LOGIN_USER_ID.remove();
    LOGIN_USER_DEPART_ID.remove();
    LOGIN_USER_ROLE_IDS.remove();
  }
}
