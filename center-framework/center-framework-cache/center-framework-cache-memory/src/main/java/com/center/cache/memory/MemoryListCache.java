package com.center.cache.memory;

import cn.hutool.cache.CacheUtil;

import com.center.cache.interfaces.Cache;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * <AUTHOR>
 * @description: list实现类
 * @date 2024/11/13 9:17
 */
@Component
public class MemoryListCache implements Cache<String, List<String>> {

    //由于hutool的cache不支持不限时间并且不限数量的缓存，所以这里设定一个长时间（100年）的缓存
    private cn.hutool.cache.Cache<String, cn.hutool.cache.Cache<String, List<String>>> listCache = CacheUtil.newTimedCache(3155760000000L);


    @Override
    public List<String> get(String key) {
        cn.hutool.cache.Cache<String, List<String>> cache = listCache.get(key);
        if (cache == null) {
            return null;
        }
        return cache.get(key);
    }

    @Override
    public void put(String key, List<String> value) {
        //这里是模仿redis逻辑，给每一个key设置一个cache，也就是每一个cache实际只会有一条内容
        cn.hutool.cache.Cache<String, List<String>> cache = CacheUtil.newLRUCache(100);
        cache.put(key, value);
        listCache.put(key, cache);
    }

    @Override
    public void put(String key, List<String> value, Long ttlTime) {
        //这里是模仿redis逻辑，给每一个key设置一个cache，也就是每一个cache实际只会有一条内容
        cn.hutool.cache.Cache<String, List<String>> cache = CacheUtil.newTimedCache(ttlTime);
        cache.put(key, value);
        listCache.put(key, cache);
    }

    @Override
    public void remove(String key) {
        cn.hutool.cache.Cache<String, List<String>> cache = listCache.get(key);
        if(cache != null){
            cache.remove(key);
            listCache.remove(key);
        }
    }
}
