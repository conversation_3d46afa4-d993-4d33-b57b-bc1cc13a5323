<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.center</groupId>
    <artifactId>huatuo-gpt</artifactId>
    <version>${revision}</version>
  </parent>

  <artifactId>center-server</artifactId>
  <description>整个项目的入口，通过引用center-huatuo_gpt工程， 从而实现提供 RESTful API 给前端项目。
    本质上来说，它就是个空壳（容器）</description>
  <packaging>jar</packaging>


  <dependencies>


    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <!--具体业务模块-->
    <dependency>
      <groupId>com.center</groupId>
      <artifactId>center-huatuo_gpt</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.center</groupId>
      <artifactId>center-infrastructure-system</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.center</groupId>
      <artifactId>center-framework-common-api</artifactId>
      <version>${revision}</version>
    </dependency>

  </dependencies>

  <build>
    <!-- 设置构建的 jar 包名 -->
    <finalName>huatuo-gpt</finalName>
    <plugins>
      <!-- 打包 -->
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.7.12</version> <!-- 如果 spring.boot.version 版本修改，则这里也要跟着修改 -->
        <configuration>
          <fork>true</fork>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>