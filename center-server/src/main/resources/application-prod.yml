spring:
  datasource:
    url: ******************************************************************************************************************************************************
    username: root
    password: center#bf9b#0006

  flyway:
    url: ********************************************************************************************************************************************
    user: root
    password: center#bf9b#0006
    table: huatuo_flyway_schema_history
  #    flyway元数据表名称，如果不指定，则默认为flyway_schema_history，多个系统共用一个库时，可以通过使用不再的表来控制数据库脚本的版本。
  #    同一个表的元数据，最好是由一个系统进行维护
  # Redis 配置
  redis:
    host: 127.0.0.1 # 地址
    port: 6379 # 端口
    database: 1 # 数据库索引
#    password: dev # 密码，建议生产环境开启
#设置本地缓存或中央缓存
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
  autoconfigure:
    exclude:
      - #排除redis自动配置类，避免redis自动创建，当启用redis时注释掉下方代码
      - org.redisson.spring.starter.RedissonAutoConfiguration
cache:
  type: memory
#  -- #################### 接口文档配置（生产环境关闭-false） ####################
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

# 或者 minio, ftp, oss 等
storage:
  type: MINIO
#hdfs配置
hadoop:
  base-path: /huatuo
  fs:
    defaultFS: hdfs://**********:8020
  dfs:
    client:
      use-datanode-hostname: true
  user:
    name: root
  home:
    dir: /usr/local/backend/center/hadoop_home
  base:
    url: http://**********:9870/webhdfs/v1/huatuo
    query: ?op=OPEN

#minio配置
minio:
  # MinIO的服务地址
  endpoint: http://**********:8002
  # MinIO的访问密钥
  access-key: admin
  # MinIO的秘密密钥
  secret-key: minio%0510
  bucket: huatuo
###################业务逻辑相关配置##########################
#创建企业管理员用户初始密码
admin:
  password: e10adc3949ba59abbe56e057f20f883e
password:
  default: 123456

huatuo-gpt:
  url: http://**********:4000/api/triageV3/question/stream
  consult_url: http://**********:4010/chat
  consult_report_url: http://**********:4010/summary

preview:
  url: http://*************:9084/huatuo/api/test/files/preview