<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
  <property name="instance" value="huatuo-gpt"/>
  <property name="log.dir" value="logs"/>
  <property name="LOG_FILE" value="/var/log/huatuo-gpt/tomcat.log"/>
  <property name="LOG_FILE_DEV" value="/var/log/huatuo-gpt/tomcat_dev.log"/>

  <springProfile name="local">
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
      <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
        <pattern>[%date{yyyy-MM-dd HH:mm:ss.SSS}][%X{TrackingId}][%thread][%level][%class][%line]:%message%n</pattern>
      </encoder>
    </appender>

    <!-- 启用 Spring WebSocket 和 STOMP 的调试日志 -->
    <logger name="org.springframework.web.socket" level="DEBUG"/>
    <logger name="org.springframework.messaging.simp" level="DEBUG"/>
    <logger name="org.springframework.messaging" level="DEBUG"/>

    <!-- 其他日志配置 -->
    <logger name="com.netflix" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="org.quartz" level="WARN"/>
    <!--<logger name="org.hibernate" level="WARN"/>-->

    <root level="INFO">
      <appender-ref ref="CONSOLE"/>
    </root>
  </springProfile>


  <springProfile name="dev">
    <appender name="ROLLING-FILE"
      class="ch.qos.logback.core.rolling.RollingFileAppender">
      <encoder>
        <pattern>[%date{yyyy-MM-dd
          HH:mm:ss.SSS}][%X{TrackingId}][%thread][%level][%class][%line]:%message%n
        </pattern>
      </encoder>
      <file>${LOG_FILE_DEV}</file>
      <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
        <fileNamePattern>${LOG_FILE_DEV}.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
        <timeBasedFileNamingAndTriggeringPolicy
          class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
          <maxFileSize>10MB</maxFileSize>
        </timeBasedFileNamingAndTriggeringPolicy>
      </rollingPolicy>
    </appender>

    <logger name="org.springframework" level="WARN"/>
    <logger name="com.netflix" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="org.quartz" level="WARN"/>
    <logger name="org.hibernate" level="WARN"/>

    <root level="INFO">
      <appender-ref ref="ROLLING-FILE"/>
    </root>
  </springProfile>

  <springProfile name="prod">
    <appender name="ROLLING-FILE"
      class="ch.qos.logback.core.rolling.RollingFileAppender">
      <encoder>
        <pattern>[%date{yyyy-MM-dd
          HH:mm:ss.SSS}][%X{TrackingId}][%thread][%level][%class][%line]:%message%n
        </pattern>
      </encoder>
      <file>${LOG_FILE}</file>
      <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
        <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
        <timeBasedFileNamingAndTriggeringPolicy
          class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
          <maxFileSize>10MB</maxFileSize>
        </timeBasedFileNamingAndTriggeringPolicy>
      </rollingPolicy>
    </appender>

    <logger name="org.springframework" level="ERROR"/>
    <logger name="springfox.documentation" level="ERROR"/>
    <logger name="com.netflix" level="ERROR"/>
    <logger name="org.apache" level="ERROR"/>
    <logger name="org.quartz" level="ERROR"/>
    <logger name="org.hibernate" level="ERROR"/>
<!--    <logger name="com.mongodb.diagnostics.logging" level="ERROR"/>-->

    <root level="INFO">
      <appender-ref ref="ROLLING-FILE"/>
    </root>
  </springProfile>
</configuration>