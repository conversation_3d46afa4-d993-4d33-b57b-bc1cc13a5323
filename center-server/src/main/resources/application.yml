server:
  port: 9084
  servlet:
    context-path: /huatuo


center:
  jwt:
    secret-key: center
    expire-in: 86400000
    default-checksum: 4662c0da71d94620a456ec8658f10064
  ignore-urls: >
    /actuator/health,
    /maven/package_time,
    /chat_registration/**,
    /chat/getTsc,
    /chat/getTts,
    /hello,
    /file/preview_url,
    /admin/auth/login/**,
    /image/**,
    /webjars/**,
    /doc.html,
    /swagger-ui/**,
    /v3/api-docs/**,
    /swagger-resources/**,
    /system/user/register,
  server-urls: > #服务间调用接口，目前不走认证
    /server_to_server/**,
    /system/user/register,
  snowflake:
    #下面的值可以基本符合大部分项目要求，如果不满足，可以根据实际情况进行修改。
    data-center-id: 1
    max-worker-id: 10
  superadmin-id: 1825843512497688576
  tenant-id: 1825838985241448448
spring:
  profiles:
    active: local
  application:
    name: center
  flyway:
    enabled: true
    baseline-on-migrate: true
  jackson:
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false # 设置 Date 的格式
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 1611460870.401，而是直接 1611460870401
      fail-on-empty-beans: false # 允许序列化无属性的 Bean
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB

  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    # druid配置
    # 连接池初始化时创建的连接数
    initialSize: 3
    # 连接池中最大连接数
    maxActive: 50
    # 连接池中最小空闲连接数
    minIdle: 5
    # 连接池中最大空闲连接数
    maxIdle: 10
    # 获取连接时的最大等待时间（毫秒）,如达到最大连接，且所有连接被使用，获取时等待的超时时间
    maxWait: 60000
    # 间隔多久检测一次空闲连接是否有效（毫秒）
    timeBetweenEvictionRunsMillis: 60000
    # 连接池中连接最小空闲时间（毫秒）,连接池中的连接被销毁的条件，连接数 > minIdle && 空闲时间 > minEvictableIdleTimeMillis
    minEvictableIdleTimeMillis: 300000
    # 连接池中连接最大空闲时间（毫秒）,连接池中的连接被销毁的条件，空闲时间 > maxEvictableIdleTimeMillis，不管连接池中的连接数是否小于最小连接数
    maxEvictableIdleTimeMillis: 25200000
    # 用于检测连接是否有效的SQL语句
    validationQuery: SELECT 1
    # 是否开启空闲连接的检测，作用类似testOnBorrow
    testWhileIdle: true
    # 是否开启连接的检测功能，在获取连接时检测连接是否有效，开启会获取连接时先执行下validationQuery配置的语句看连接是否有效，所以会影响性能，
    testOnBorrow: false
    # 是否开启连接的检测功能，在归还连接时检测连接是否有效，此参数必须和testOnBorrow同时设置为true当服务端关闭，客户端才不会报错
    testOnReturn: false
    # 是否缓存PreparedStatement对象
    poolPreparedStatements: true
    # 缓存PreparedStatement对象的最大数量
    maxPoolPreparedStatementPerConnectionSize: 20
    # 配置监控统计用的filter，允许监控统计
    filters: stat
    # 配置扩展属性，用于监控统计分析SQL性能等
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

  jpa:
    database-platform: org.hibernate.dialect.MySQLDialect
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        jdbc:
          lob:
            non_contextual_creation: true
    hibernate:
      ddl-auto: none
    open-in-view: false



#  -- #################### 接口文档配置 ####################

springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui
  default-flat-param-object: true # 参见 https://doc.xiaominfo.com/docs/faq/v4/knife4j-parameterobject-flat-param 文档


logging:
  config: classpath:logback-spring.xml

app:
  version: '@project.version@'
  build:
    time: '@timestamp@'

management:
  endpoints:
    web:
      exposure:
        include: health
