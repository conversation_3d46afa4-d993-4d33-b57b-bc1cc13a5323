-- 删除 `ctime` 字段
ALTER TABLE `huatuo_consultation_chat_answer`
DROP COLUMN `ctime`;

-- 增加新的字段
ALTER TABLE `huatuo_consultation_chat_answer`
    ADD COLUMN `creator_id` BIGINT DEFAULT NULL COMMENT '创建人' AFTER `languages`,
ADD COLUMN `create_time` DATETIME DEFAULT NULL COMMENT '创建时间' AFTER `creator_id`,
ADD COLUMN `updater_id` BIGINT DEFAULT NULL COMMENT '更新人' AFTER `create_time`,
ADD COLUMN `update_time` DATETIME DEFAULT NULL COMMENT '更新时间' AFTER `updater_id`;

-- 删除 `ctime` 字段
ALTER TABLE `huatuo_consultation_chat_question`
DROP COLUMN `ctime`;

-- 增加新的字段
ALTER TABLE `huatuo_consultation_chat_question`
    ADD COLUMN `creator_id` BIGINT DEFAULT NULL COMMENT '创建人' AFTER `session_id`,
ADD COLUMN `create_time` DATETIME DEFAULT NULL COMMENT '创建时间' AFTER `creator_id`,
ADD COLUMN `updater_id` BIGINT DEFAULT NULL COMMENT '更新人' AFTER `create_time`,
ADD COLUMN `update_time` DATETIME DEFAULT NULL COMMENT '更新时间' AFTER `updater_id`;


-- 增加新的字段
ALTER TABLE `huatuo_consultation_chat_session`
    ADD COLUMN `creator_id` BIGINT DEFAULT NULL COMMENT '创建人' AFTER `appointment_id`,
ADD COLUMN `create_time` DATETIME DEFAULT NULL COMMENT '创建时间' AFTER `creator_id`,
ADD COLUMN `updater_id` BIGINT DEFAULT NULL COMMENT '更新人' AFTER `create_time`,
ADD COLUMN `update_time` DATETIME DEFAULT NULL COMMENT '更新时间' AFTER `updater_id`;