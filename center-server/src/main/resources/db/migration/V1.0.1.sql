CREATE DATABASE IF NOT EXISTS huatuo_gpt;
use huatuo_gpt;
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for chat_answer
-- ----------------------------
DROP TABLE IF EXISTS `chat_answer`;
CREATE TABLE `chat_answer`
(
    `id`              bigint(20)                            NOT NULL,
    `content`         mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回答内容',
    `ctime`           datetime                              NOT NULL COMMENT '创建时间',
    `source`          int(2)                                NOT NULL COMMENT '回答来源。1 华佗GPT生成的答案，4chatGPT生成的答案',
    `mark`            int(2)                                NOT NULL COMMENT '评分，-1 踩，0 默认，1 赞',
    `question_id`     bigint(20)                            NOT NULL COMMENT '问题id',
    `session_id`      bigint(20)                            NOT NULL COMMENT '对话id',
    `raw_answer`      mediumtext COLLATE utf8mb4_unicode_ci COMMENT '未进行后处理的answer',
    `sensitive_words` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '敏感词jsonstring',
    `languages`       varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'all' COMMENT '语言，all表示不区分语言',
    PRIMARY KEY (`id`),
    KEY `sessionid` (`session_id`),
    KEY `questionid` (`question_id`)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_unicode_ci;



-- ----------------------------
-- Table structure for chat_question
-- ----------------------------
DROP TABLE IF EXISTS `chat_question`;
CREATE TABLE `chat_question`
(
    `id`         bigint(20)                            NOT NULL,
    `content`    mediumtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '对话内容',
    `clazz`      int(2)     DEFAULT NULL COMMENT '问题分类 1医疗 2其他',
    `session_id` bigint(20) DEFAULT NULL COMMENT '对话id',
    `ctime`      datetime                              NOT NULL COMMENT '创建时间',
    `user_id`    bigint(20) DEFAULT '0',
    PRIMARY KEY (`id`),
    KEY `sessionid` (`session_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;


-- ----------------------------
-- Table structure for chat_session
-- ----------------------------
DROP TABLE IF EXISTS `chat_session`;
CREATE TABLE `chat_session`
(
    `id`         bigint(20) NOT NULL,
    `ctime`      datetime   NOT NULL COMMENT '创建时间',
    `etime`      datetime                                DEFAULT NULL COMMENT '结束时间',
    `score`      int(2)                                  DEFAULT '0' COMMENT '评分，-1 踩，0 默认，1 赞',
    `model_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '调用的模型名称',
    PRIMARY KEY (`id`),
    KEY `modelname` (`model_name`),
    KEY `ctime` (`ctime`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

