
ALTER TABLE `center_system_user`
    ADD COLUMN `identity_number` varchar(18) NOT NULL COMMENT '身份证号';
ALTER TABLE `center_system_user`
    ADD COLUMN `user_marital_status` bit NULL COMMENT '婚姻状态';
ALTER TABLE `center_system_user`
    ADD COLUMN `user_last_name` varchar(50) NOT NULL COMMENT '用户姓氏';
ALTER TABLE `center_system_user`
    ADD COLUMN `user_first_name` varchar(50) NOT NULL COMMENT '用户名字';


CREATE TABLE `huatuo_user_disease_history`
(
    `id`            bigint(20) NOT NULL  COMMENT '主键ID',
    `user_id`       bigint(20) NOT NULL COMMENT '用户ID',
    `disease_history`   text NOT NULL COMMENT '病史描述',
    `disease_type`        varchar(50)  NOT NULL COMMENT '病史类型',
    `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
    `create_time`   datetime DEFAULT NULL COMMENT '创建时间',
    `update_time`   datetime DEFAULT NULL COMMENT '更新时间',
    `creator_id`    bigint(20) DEFAULT NULL COMMENT '创建者',
    `updater_id`    bigint(20) DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户疾病史表';
