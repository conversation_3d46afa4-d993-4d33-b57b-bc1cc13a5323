-- Appointments Table
CREATE TABLE huatuo_appointments (
                                     id BIGINT(20) AUTO_INCREMENT PRIMARY KEY COMMENT '挂号记录唯一标识',
                                     user_id BIGINT(20) NOT NULL COMMENT '用户id',
                                     hospital_department_id BIGINT(20) NOT NULL COMMENT '挂号科室id',
                                     departments_name VARCHAR(255) COMMENT '科室名称',
                                     doctor_id BIGINT(20) COMMENT '医生id',
                                     doctor_name VARCHAR(255) COMMENT '医生姓名',
                                     registration_type VARCHAR(50) COMMENT '挂号类型',
                                     doctor_level VARCHAR(255) COMMENT '医生级别（例如：主任医师、副主任医师、医师等）',
                                     appointment_date DATE NOT NULL COMMENT '预约日期',
                                     appointment_start_time TIME NOT NULL COMMENT '预约开始时间',
                                     appointment_end_time TIME NOT NULL COMMENT '预约结束时间',
                                     visiting_time DATETIME COMMENT '就诊时间',
                                     status VARCHAR(255) COMMENT '挂号状态，未就诊、已就诊、取消',
                                     latest_sessionId BIGINT(20) COMMENT '最新会话id',
                                     tenant_id BIGINT(20) COMMENT '租户号',
                                     creator_id BIGINT(20) COMMENT '创建人',
                                     create_time DATETIME COMMENT '创建时间',
                                     updater_id BIGINT(20) COMMENT '更新人',
                                     update_time DATETIME COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='挂号信息表';

-- Medical Records Table
CREATE TABLE huatuo_medical_records (
                                        id BIGINT(20) AUTO_INCREMENT PRIMARY KEY COMMENT '电子病历唯一标识',
                                        appointment_id BIGINT(20) NOT NULL COMMENT '挂号id',
                                        chief_complaint TEXT COMMENT '主诉',
                                        present_illness TEXT COMMENT '现病史',
                                        past_medical_history TEXT COMMENT '既往病史',
                                        family_medical_history TEXT COMMENT '家族病史',
                                        allergy_history TEXT COMMENT '过敏史',
                                        medical_records_status VARCHAR(255) COMMENT '问诊报告状态',
                                        tenant_id BIGINT(20) COMMENT '租户号',
                                        creator_id BIGINT(20) COMMENT '创建人',
                                        create_time DATETIME COMMENT '创建时间',
                                        updater_id BIGINT(20) COMMENT '更新人',
                                        update_time DATETIME COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='问诊报告表';

-- Reviews Table
CREATE TABLE huatuo_reviews (
                                id BIGINT(20) AUTO_INCREMENT PRIMARY KEY COMMENT '评价记录唯一标识',
                                medical_records_id BIGINT(20) NOT NULL COMMENT '问诊报告ID',
                                rating INT NOT NULL COMMENT '评分（1-5星）',
                                review_time DATETIME COMMENT '评价时间',
                                feedback TEXT COMMENT '患者的反馈',
                                tenant_id BIGINT(20) COMMENT '租户号',
                                creator_id BIGINT(20) COMMENT '创建人',
                                create_time DATETIME COMMENT '创建时间',
                                updater_id BIGINT(20) COMMENT '更新人',
                                update_time DATETIME COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评价信息表';

-- File Attachments Table
CREATE TABLE huatuo_file_attachments (
                                         id BIGINT(20) AUTO_INCREMENT PRIMARY KEY COMMENT '附件唯一标识',
                                         file_path VARCHAR(255) NOT NULL COMMENT '文件存储路径',
                                         file_name VARCHAR(255) COMMENT '文件名称',
                                         file_type VARCHAR(255) COMMENT '文件类型，例如图片、音频、文档等',
                                         upload_time DATETIME COMMENT '上传时间',
                                         tenant_id BIGINT(20) COMMENT '租户号',
                                         creator_id BIGINT(20) COMMENT '创建人',
                                         create_time DATETIME COMMENT '创建时间',
                                         updater_id BIGINT(20) COMMENT '更新人',
                                         update_time DATETIME COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='附件表';

-- Doctors Table
CREATE TABLE huatuo_doctors (
                                id BIGINT(20) AUTO_INCREMENT PRIMARY KEY COMMENT '医生唯一标识',
                                doctor_name VARCHAR(255) NOT NULL COMMENT '医生姓名',
                                doctor_gender BIT(1) COMMENT '医生性别',
                                hospital_department_id BIGINT(20) NOT NULL COMMENT '所属科室ID',
                                doctor_level VARCHAR(255) COMMENT '医生级别（例如：主任医师、副主任医师、医师等）',
                                doctor_phone VARCHAR(255) COMMENT '手机号码',
                                doctor_status BIT(1) COMMENT '医生状态，0表示离职，1表示在职或者表示出勤',
                                tenant_id BIGINT(20) COMMENT '租户号',
                                creator_id BIGINT(20) COMMENT '创建人',
                                create_time DATETIME COMMENT '创建时间',
                                updater_id BIGINT(20) COMMENT '更新人',
                                update_time DATETIME COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医生表';

-- Hospital Departments Table
CREATE TABLE huatuo_hospital_departments (
                                             id BIGINT(20) AUTO_INCREMENT PRIMARY KEY COMMENT '科室唯一标识',
                                             departments_name VARCHAR(255) NOT NULL COMMENT '科室名称',
                                             tenant_id BIGINT(20) COMMENT '租户号',
                                             creator_id BIGINT(20) COMMENT '创建人',
                                             create_time DATETIME COMMENT '创建时间',
                                             updater_id BIGINT(20) COMMENT '更新人',
                                             update_time DATETIME COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='科室表';

-- Consultation Chat Answer Table
CREATE TABLE huatuo_consultation_chat_answer (
                                                 id BIGINT(20) AUTO_INCREMENT PRIMARY KEY COMMENT '问诊答案唯一标识',
                                                 content MEDIUMTEXT NOT NULL COMMENT '回答内容',
                                                 ctime DATETIME NOT NULL COMMENT '创建时间',
                                                 source INT NOT NULL COMMENT '回答来源。1 华佗GPT生成的答案，4 chatGPT生成的答案',
                                                 mark INT NOT NULL COMMENT '评分，-1 踩，0 默认，1 赞',
                                                 question_id BIGINT(20) NOT NULL COMMENT '问题id',
                                                 session_id BIGINT(20) NOT NULL COMMENT '对话id',
                                                 raw_answer MEDIUMTEXT COMMENT '未进行后处理的answer',
                                                 sensitive_words MEDIUMTEXT COMMENT '敏感词jsonstring',
                                                 languages VARCHAR(20) DEFAULT 'all' COMMENT '语言，all表示不区分语言'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='华佗问诊答案表';

-- Consultation Chat Question Table
CREATE TABLE huatuo_consultation_chat_question (
                                                   id BIGINT(20) AUTO_INCREMENT PRIMARY KEY COMMENT '问诊问题唯一标识',
                                                   content MEDIUMTEXT NOT NULL COMMENT '对话内容',
                                                   clazz INT COMMENT '问题分类 1医疗 2其他',
                                                   session_id BIGINT(20) COMMENT '对话id',
                                                   ctime DATETIME NOT NULL COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='华佗问诊问题表';

-- Consultation Chat Session Table
CREATE TABLE huatuo_consultation_chat_session (
                                                  id BIGINT(20) AUTO_INCREMENT PRIMARY KEY COMMENT '问诊会话唯一标识',
                                                  user_id BIGINT(20) NOT NULL COMMENT '用户id',
                                                  appointment_id BIGINT(20) NOT NULL COMMENT '挂号id',
                                                  ctime DATETIME NOT NULL COMMENT '创建时间',
                                                  etime DATETIME COMMENT '结束时间',
                                                  score INT DEFAULT 0 COMMENT '评分，-1 踩，0 默认，1 赞',
                                                  model_name VARCHAR(255) COMMENT '调用的模型名称'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='华佗问诊会话表';
