CREATE TABLE center_depart (
                               `id` BIGINT(20) NOT NULL COMMENT '主键',
                               `name` varchar(100) NOT NULL COMMENT '部门名称',
                               `parent_id` BIGINT NULL COMMENT '上级部门ID',
                               `sort` INT NULL COMMENT '显示顺序',
                               `tenant_id` BIGINT(20) NOT NULL COMMENT '租户ID',
                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                               `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                               `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                               `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                               PRIMARY KEY (`id`) USING BTREE
)
    ENGINE=InnoDB
    DEFAULT CHARSET=utf8mb4
    COLLATE=utf8mb4_unicode_ci
    COMMENT='部门表';

CREATE TABLE `center_system_tenant` (
                                        `id` bigint(20) NOT NULL  COMMENT '租户ID',
                                        `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户名',
                                        `description` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '备注说明',
                                        `status` varchar(10) NOT NULL COMMENT '租户状态',
                                        `expire_time` datetime NOT NULL COMMENT '过期时间',
                                        `account_count` int(11) NOT NULL COMMENT '账号数量',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                        `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户表';


CREATE TABLE `center_system_user` (
                                      `id` bigint(20) NOT NULL COMMENT '用户ID',
                                      `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户账号',
                                      `password` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密码',
                                      `display_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
                                      `status` varchar(10) NOT NULL COMMENT '帐号状态',
                                      `login_ip` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '最后登录IP',
                                      `login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                      `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                      `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

CREATE TABLE `center_system_role` (
                                      `id` bigint(20) NOT NULL COMMENT '角色ID',
                                      `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
                                      `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色代码',
                                      `sort` int(11) NOT NULL COMMENT '显示顺序',
                                      `status` varchar(10) DEFAULT NULL COMMENT '角色状态',
                                      `remark` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                      `tenant_id` bigint(20) NOT NULL COMMENT '租户编号',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                      `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色信息表';


CREATE TABLE `center_system_menu` (
                                      `id` bigint(20) NOT NULL COMMENT '菜单ID',
                                      `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
                                      `category` varchar(10) NOT NULL COMMENT '菜单类型',
                                      `sort` int(11) NOT NULL DEFAULT '0' COMMENT '显示顺序',
                                      `parent_id` bigint(20) NOT NULL  COMMENT '父菜单ID',
                                      `path` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '路由地址',
                                      `icon` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '#' COMMENT '菜单图标',
                                      `status` varchar(10) DEFAULT NULL COMMENT '菜单状态',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                      `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                      `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单表';

CREATE TABLE `center_system_user_role` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                           `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                                           `role_id` bigint(20) NOT NULL COMMENT '角色ID',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                           `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                           `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户和角色关联表';



CREATE TABLE `center_system_role_menu` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                           `role_id` bigint(20) NOT NULL COMMENT '角色ID',
                                           `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
                                           `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                           `creator_id` bigint(20) DEFAULT NULL COMMENT '创建者',
                                           `updater_id` bigint(20) DEFAULT NULL COMMENT '更新者',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色和菜单关联表';

INSERT INTO center_system_tenant
(id, name, description, status, expire_time, account_count, create_time, update_time, creator_id, updater_id)
VALUES(1825838985241448448, '深圳大数据研院无锡创新中心', '深圳大数据研院无锡创新中心', 'ACTIVE', '2099-12-31 23:59:59', 100, current_timestamp, current_timestamp, **********497688576, **********497688576);

INSERT INTO `center_system_user`
(id, username, password, display_name, status, login_ip, login_time, create_time, update_time, creator_id, updater_id, tenant_id)
VALUES(**********497688576, 'superadmin', '867b67919f24871f189784d933d558df', '超级管理员', 'ACTIVE', NULL, NULL, current_timestamp,current_timestamp,**********497688576, **********497688576, 1825838985241448448);

INSERT INTO center_system_role
(id, name, code, sort, status, remark, tenant_id, create_time, update_time, creator_id, updater_id)
VALUES(1826081597714087936, '超级管理员', 'SUPERADMIN', 1, 'ACTIVE', '系统超级管理员', 1825838985241448448,current_timestamp,current_timestamp, **********497688576, **********497688576);

INSERT INTO center_system_user_role
(id, user_id, role_id, create_time, update_time, creator_id, updater_id)
VALUES(1826082885449302016, **********497688576, 1826081597714087936, current_timestamp,current_timestamp, **********497688576, **********497688576);
