package com.center.infrastructure.system.biz.user.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class PasswordReq {

    @Schema(description = "旧密码")
    @NotBlank(message = "旧密码不能为空")
    private String password;

    @Schema(description = "新密码")
    @NotBlank(message = "新密码不能为空")
    private String newPassword;

    @Schema(description = "确认新密码")
    @NotBlank(message = "新密码不能为空")
    private String newPassword2;
}
