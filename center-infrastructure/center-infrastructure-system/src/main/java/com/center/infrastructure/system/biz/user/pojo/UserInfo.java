package com.center.infrastructure.system.biz.user.pojo;

import com.center.framework.common.enumerate.CommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UserInfo {
    @Schema(description = "用户id")
    private Long id;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "昵称")
    private String displayName;

    @Schema(description = "手机号")
    private String phoneNumber;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "用户状态")
    private CommonStatusEnum status;

    @Schema(description = "身份证号")
    private String identityNumber;

    @Schema(description = "婚姻状态")
    private Boolean userMaritalStatus;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "用户姓氏", example = "张")
    private String userLastName;

    @Schema(description = "用户名字", example = "三")
    private String userFirstName;

}
