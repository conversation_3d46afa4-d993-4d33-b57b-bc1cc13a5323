package com.center.infrastructure.system.biz.user.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public class UserRegisterReq {

    @Schema(description = "用户名字",example = "小草")
    @NotBlank(message = "用户名字不能为空")
    private String userFirstName;

    @Schema(description = "用户姓氏",example = "秦")
    @NotBlank(message = "用户姓氏不能为空")
    private String userLastName;

    @Schema(description = "手机号",requiredMode = Schema.RequiredMode.REQUIRED,example = "123456789")
    @NotNull(message = "手机号不能为空")
    @Pattern(regexp = "^\\d{11}$", message = "手机号必须为11位数字")
    private String phoneNumber;

    @Schema(description = "身份证号",requiredMode = Schema.RequiredMode.REQUIRED,example = "341333333333333333")
    @NotNull(message = "身份证号不能为空")
    @Pattern(regexp = "^\\d{6}(18|19|20)?\\d{2}(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])\\d{3}(\\d|X|x)$", message = "身份证号校验不通过")
    private String identityNumber;
}
