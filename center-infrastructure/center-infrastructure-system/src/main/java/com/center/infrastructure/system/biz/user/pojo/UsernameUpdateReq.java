package com.center.infrastructure.system.biz.user.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class UsernameUpdateReq {
    @Schema(description = "用户id")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @Schema(description = "用户姓氏", example = "张")
    @NotBlank(message = "用户姓氏不能为空")
    private String userLastName;

    @Schema(description = "用户名字", example = "三")
    @NotBlank(message = "用户名字不能为空")
    private String userFirstName;

}
