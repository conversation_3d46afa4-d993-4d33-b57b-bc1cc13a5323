package com.center.infrastructure.system.biz.user.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public class IdentityNumberReq {
    @Schema(description = "用户id",example = "1864898503257501696")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @Schema(description = "身份证号",requiredMode = Schema.RequiredMode.REQUIRED,example = "341227188903028019")
    @NotNull(message = "身份证号不能为空")
    @Pattern(regexp = "^\\d{6}(18|19|20)?\\d{2}(0[1-9]|1[012])(0[1-9]|[12][0-9]|3[01])\\d{3}(\\d|X|x)$", message = "身份证号校验不通过")
    private String identityNumber;
}
