package com.center.infrastructure.system.biz.user;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvertPoint;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.depart.pojo.DepartResp;
import com.center.infrastructure.system.biz.role.pojo.RoleResp;
import com.center.infrastructure.system.biz.user.pojo.*;
import com.center.infrastructure.system.biz.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "系统基础功能-用户管理")
@RestController
@RequestMapping("/system/user")
@Validated
@Slf4j
public class UserController {

  @Resource
  private UserService userService;

  @PostMapping("/register")
  @Operation(summary = "用户注册接口",description = "")
  @Parameter(description = "新用户的基本信息")
  public CommonResult<String> register(@RequestBody @Valid UserRegisterReq userRegisterReq){
    userService.register(userRegisterReq);
    return CommonResult.successWithMessageOnly("注册成功");
  }

  @PostMapping("/save_username")
  @Parameter(description = "更新后的用户姓和名")
  @Operation(summary = "修改用户姓名")
  public CommonResult<String> saveUsername(@RequestBody @Valid UsernameUpdateReq userNameUpdateReq) {
    userService.saveUsername(userNameUpdateReq);
    return CommonResult.successWithMessageOnly("用户姓名修改成功");
  }

  @PostMapping("/save_identity_number")
  @Parameter(description = "更新后的身份证号")
  @Operation(summary = "修改用户身份证号")
  public CommonResult<String> saveIdentityNumber(@RequestBody @Valid IdentityNumberReq identityNumberReq) {
    userService.saveIdentityNumber(identityNumberReq);
    return CommonResult.successWithMessageOnly("用户身份证号修改成功");
  }

  @PostMapping("/set_password")
  @Operation(summary = "设置密码")
  public CommonResult<String> setPassword(@RequestBody @Valid PasswordReq passwordReq) {
    log.info("设置密码：{}",passwordReq);
    userService.setPassword(passwordReq);
    return CommonResult.successWithMessageOnly("密码设置成功");
  }


  @PostMapping("/create")
  @Operation(summary = "创建租户下的用户", description = "保存用户的租户和角色信息")
  @Parameter(description = "新用户的基本信息")
  public CommonResult<String> save(@RequestBody @Valid UserCreateReq userCreateReq) {
    log.info("创建的新用户信息：{}",userCreateReq);
    userService.save(userCreateReq);
    return CommonResult.successWithMessageOnly("用户创建成功");
  }

  @PostMapping("/update")
  @Parameter(description = "更新的用户信息")
  @Operation(summary = "修改用户基本信息")
  public CommonResult<String> update(@RequestBody @Valid UserUpdateReq userUpdateReq) {
    log.info("更新用户信息：{}",userUpdateReq);
    userService.update(userUpdateReq);
    return CommonResult.successWithMessageOnly("用户修改成功");
  }

  @PostMapping("/delete/{id}")
  @Parameter(description = "用户ID")
  @Operation(summary = "删除用户", description = "假删除")
  public CommonResult<String> delete(@PathVariable Long id) {
    log.info("删除用户：{}",id);
    userService.delete(id);
    return CommonResult.successWithMessageOnly("删除用户成功");
  }

  @GetMapping("/get")
  @Parameter(description = "用户ID")
  @Operation(summary = "根据ID查看用户基本信息")
  @EnumConvertPoint
  public CommonResult<UserResp> get(@RequestParam(name = "id") Long id) {
    log.info("查看用户信息：{}",id);
    return CommonResult.success(userService.get(id));
  }

  @GetMapping("/user_page")
  @Parameter(description = "分页参数")
  @Operation(summary = "分页查询用户信息")
  @EnumConvertPoint
  public CommonResult<PageResult<UserPageView>> page(@Valid UserPageReq userPageReq) {
    log.info("分页查询用户信息：{}",userPageReq);
    PageResult<UserPageView> page = userService.page(userPageReq);
    return CommonResult.success(page);
  }


  @PostMapping("/active/{id}")
  @Operation(summary = "启用用户")
  public CommonResult<String> active(@PathVariable @Valid Long id) {
    log.info("启用用户：{}",id);
    userService.updateStatus(id, CommonStatusEnum.ACTIVE);
    return CommonResult.successWithMessageOnly("用户启用成功");
  }

  @PostMapping("/inactive/{id}")
  @Operation(summary = "停用用户")
  public CommonResult<String> inactive(@PathVariable @Valid Long id) {
    log.info("停用用户：{}",id);
    userService.updateStatus(id,CommonStatusEnum.INACTIVE);
    return CommonResult.successWithMessageOnly("用户停用成功");
  }

  @GetMapping("/getRole")
  @Operation(summary = "查询可用角色信息")
  @EnumConvertPoint
  public CommonResult<List<RoleResp>> getRole() {
    log.info("查询可用角色信息中...");
    List<RoleResp> roleList= userService.getRole();
    return CommonResult.success(roleList);
  }

  @GetMapping("/getDepart")
  @Operation(summary = "查询可用部门信息")
  @EnumConvertPoint
  public CommonResult<List<DepartResp>> getDepart() {
    log.info("查询可用部门信息中...");
    List<DepartResp> departList = userService.getDepart();
    return CommonResult.success(departList);
  }
}
