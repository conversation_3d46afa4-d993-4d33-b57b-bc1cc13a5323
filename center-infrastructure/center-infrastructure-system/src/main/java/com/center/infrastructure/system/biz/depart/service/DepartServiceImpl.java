package com.center.infrastructure.system.biz.depart.service;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import com.center.cache.factory.CacheFactory;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.Tree.TreeNodeUtils;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.infrastructure.system.biz.depart.persistence.DepartModel;
import com.center.infrastructure.system.biz.depart.persistence.DepartRepository;
import com.center.infrastructure.system.biz.depart.persistence.QDepartModel;
import com.center.infrastructure.system.biz.depart.pojo.DepartAndKbListResp;
import com.center.infrastructure.system.biz.depart.pojo.DepartCreateReq;
import com.center.infrastructure.system.biz.depart.pojo.DepartResp;
import com.center.infrastructure.system.biz.depart.pojo.DepartUpdateReq;
import com.center.infrastructure.system.biz.tenant.persitence.TenantRepository;
import com.center.infrastructure.system.biz.user.persistence.UserRepository;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.*;

/**
 * 部门管理服务实现类
 */
@Service
public class DepartServiceImpl implements DepartService {

    @Resource
    private DepartRepository departRepository;
    @Resource
    private JPAQueryFactory queryFactory;

    private static final String pathSeparator = "/";
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private TenantRepository tenantRepository;
    @Resource
    private CacheFactory cacheFactory;

    @Override
    public List<DepartAndKbListResp> getDepart(Long departId, String path) {
        return listDeparts(departId, path, false);
    }

    @Override
    public List<DepartAndKbListResp> getDepartKb(Long departId, String path) {
        return listDeparts(departId, path, true);
    }

    /**
     * 创建部门
     *
     * @param req 部门创建请求
     */
    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 2)
    public void createDepart(DepartCreateReq req) {
        // 校验部门名称是否重复（同一层级内不可重复）
        if (departRepository.existsByDepartNameAndParentId(req.getDepartName(), req.getParentId())) {
            throw ServiceExceptionUtil.exception(DUPLICATED_OBJECT, "该层级下部门名称重复");
        }
        // 校验上级部门是否存在
        if (!departRepository.existsById(req.getParentId())) {
            throw ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "上级部门不存在");
        }

        // 将创建请求转换为部门实体
        DepartModel model = OrikaUtils.convert(req, DepartModel.class);
        model.setTenantId(LoginContextHolder.getLoginUserTenantId());
        // 保存部门
        departRepository.save(model);
        // 获取部门树中当前部门的所有父部门ID
        List<String> parentsIdstr = TreeUtil.getParentsId((Tree<String>)cacheFactory.getHashCache().get(model.getParentId().toString()), true);
        List<Long> parentsId = parentsIdstr.stream().filter(id -> id != null).map(Long::valueOf).collect(Collectors.toList());
        // 移除租户部门树缓存
        cacheFactory.getHashCache().remove(model.getTenantId().toString());
        // 重新加载租户的部门树信息到缓存中
        loadTenantDepartTree(model.getTenantId());
        // 移除部门树信息的缓存，以准备更新最新的部门树信息
        for (Long parentId : parentsId) {
            cacheFactory.getHashCache().remove(parentId.toString());
        }
        //重新加载部门树信息到缓存中
        loadDepartTrees(parentsId, model.getTenantId());
        loadDepartTree(model.getId(), model.getTenantId());
    }

    /**
     * 修改部门名称
     *
     * @param req 部门更新请求
     */
    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 2)
    public void updateDepart(DepartUpdateReq req) {
        // 根据ID查找部门
        DepartModel model = departRepository.findById(req.getId())
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "部门不存在"));

        // 校验部门名称是否重复（同一层级内，且排除当前部门）
        if (departRepository.existsByDepartNameAndParentIdAndTenantIdAndIdNot(req.getName(), model.getParentId(), model.getTenantId(), req.getId())) {
            throw ServiceExceptionUtil.exception(DUPLICATED_OBJECT, "该 层级下部门名称重复");
        }

        // 更新部门信息
        model.setDepartName(req.getName());
        model.setUpdateTime(LocalDateTime.now());
        model.setUpdaterId(LoginContextHolder.getLoginUserId());

        // 保存更新后的部门
        departRepository.save(model);
        // 获取部门树中当前部门的所有父部门ID
        List<String> parentsIdstr = TreeUtil.getParentsId((Tree<String>)cacheFactory.getHashCache().get(model.getId().toString()), true);
        List<Long> parentsId = parentsIdstr.stream().filter(id -> id != null).map(Long::valueOf).collect(Collectors.toList());
        // 移除租户部门树缓存
        cacheFactory.getHashCache().remove(model.getTenantId().toString());
        // 重新加载租户的部门树信息到缓存中
        loadTenantDepartTree(model.getTenantId());
        // 移除部门树信息的缓存，以准备更新最新的部门树信息
        for (Long parentId : parentsId) {
            cacheFactory.getHashCache().remove(parentId.toString());
        }
        //重新加载部门树信息到缓存中
        loadDepartTrees(parentsId, model.getTenantId());
    }

    /**
     * 删除部门
     *
     * @param departId 部门ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 2)
    public void deleteDepart(Long departId) {
        isTopDepart(departId);
        List<Long> subDepartmentsId = findAllSubDepartments(departId);
        List<String> parentsIdstr = TreeUtil.getParentsId((Tree<String>)cacheFactory.getHashCache().get(departId.toString()), true);
        List<Long> parentsId = parentsIdstr.stream().filter(id -> id != null).map(Long::valueOf).collect(Collectors.toList());
        //检查该部门下是否存在用户（包括子部门）
        checkDepartUsers(departId);
        //删除部门（包括子部门）
        List<Long> allSubDepartments = findAllSubDepartments(departId);
        departRepository.deleteAllById(allSubDepartments);
        Long tanentId = LoginContextHolder.getLoginUserTenantId();
        List<Long> departIds = CollUtil.newArrayList();
        departIds.addAll(parentsId);
        departIds.addAll(allSubDepartments);
        // 移除租户部门树缓存
        cacheFactory.getHashCache().remove(tanentId.toString());
        // 重新加载租户的部门树信息到缓存中
        loadTenantDepartTree(tanentId);
        // 移除部门树信息的缓存，以准备更新最新的部门树信息
        for (Long departid : departIds) {
            cacheFactory.getHashCache().remove(departid.toString());
        }
        //重新加载部门树信息到缓存中
        loadDepartTrees(parentsId, tanentId);
        for (Long subDepartmentId : subDepartmentsId) {
            cacheFactory.getListCache().remove(subDepartmentId.toString());
        }
    }

    private void isTopDepart(Long departId) {
        DepartModel departModel = departRepository.findById(departId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "部门不存在"));
        if (departModel.getParentId() == 0) {
            throw ServiceExceptionUtil.exception(FORBIDDEN, "顶级部门禁止删除");
        }
    }


    /**
     * 检查部门及其子部门下是否存在用户
     * <p>
     * 此方法的目的是在删除部门之前，检查该部门及其所有子部门下是否还有用户存在如果存在用户，则抛出异常，提示部门下存在用户
     * 这用于防止在部门仍有用户的情况下错误地删除部门，确保数据的完整性和一致性
     *
     * @param id 部门的唯一标识符，用于查询部门及其子部门的信息
     * @throws ServiceExceptionUtil 当部门下存在用户时抛出异常，提示部门不能被删除
     */
    private void checkDepartUsers(Long id) {

        // 遍历当前部门的所有子部门，检查当前部门下是否存在用户，如果存在则抛出异常
        findAllSubDepartments(id).forEach(subDepartId -> {
            if (userRepository.existsByDepartId(id)) {
                throw ServiceExceptionUtil.exception(DELETE_OBJECT_ERROR, "部门下存在用户，请手动移除所有用户后重试！");
            }
        });

    }

    /**
     * 获取部门详情
     *
     * @param id 部门ID
     * @return 部门详情
     */
    @Override
    public DepartResp getDepartById(Long id) {
        DepartModel model = departRepository.findById(id)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "部门不存在"));
        return OrikaUtils.convert(model, DepartResp.class);
    }

    /**
     * 根据上级部门ID获取部门列表
     *
     * @param parentId 上级部门ID
     * @return 部门列表
     */
    @Override
    public List<DepartResp> getDepartsByParentId(Long parentId) {
        List<DepartModel> departList = departRepository.findByParentIdAndTenantId(parentId, LoginContextHolder.getLoginUserTenantId());
        return OrikaUtils.convertList(departList, DepartResp.class);
    }

    /**
     * 根据租户ID获取部门列表
     *
     * @param tenantId 租户ID
     * @return 部门列表
     */
    @Override
    public List<DepartResp> getDepartsByTenantId(Long tenantId) {
        List<DepartModel> departList = departRepository.findByTenantId(tenantId);
        return OrikaUtils.convertList(departList, DepartResp.class);
    }

    @Override
    public boolean checkDepartmentPermission(Long userDeptId, Long targetDeptId) {
        // 查询是否目标部门是用户部门或下级部门
        return departRepository.existsByParentId(userDeptId, targetDeptId);
    }

    @Override
    public List<Long> getAccessibleDepartmentIds(Long userDeptId) {
        // 获取当前用户部门及其下级部门的ID
        return departRepository.findAllByParentId(userDeptId);
    }


    /**
     * 获取部门及其知识库列表
     *
     * @param departId 部门ID，如果为null，则表示请求的是根部门
     * @param path     用于记录当前请求的路径，确保部门ID不为空时，path不能为空
     * @return 返回一个包含部门信息的列表
     */
    public List<DepartAndKbListResp> listDeparts(Long departId, String path, Boolean ischeck) {
        //生成部门列表
        List<DepartModel> departModelList = new ArrayList<>();
        //获取租户ID
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        //获取用户所在部门ID
        Long userDepartId = LoginContextHolder.getLoginUserDepartId();
        //生成部门ID列表
        List<Long> departIds = new ArrayList<>();
        if (departId == null) {
            //如果部门ID为空，则表示请求的是根部门
            getDepartModelListAndIdsList(tenantId, 0L, departModelList, departIds);
        } else {
            //部门ID不为空时，path不能为空
            if (path.isEmpty()) {
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.REQUEST_PARAM_ERROR, "部门ID不为空时，path不能为空");
            }
            //判断当前部门是否为根部门
            List<Long> checkList = new ArrayList<>();
            departRepository.findByTenantIdAndParentId(tenantId, 0L).forEach(departModel -> {
                checkList.add(departModel.getId());
            });
            if (checkList.contains(departId) && ischeck) {
                //如果当前部门是根部门，则直接返回用户所在部门
                DepartModel departModel = departRepository.findById(userDepartId)
                        .orElseThrow(() -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "该角色所在部门不存在"));
                departModelList.add(departModel);
                departIds.add(departModel.getId());
            } else {
                //如果当前部门不是根部门，则根据部门ID获取其子部门
                getDepartModelListAndIdsList(tenantId, departId, departModelList, departIds);
            }
        }
        //获取所有部门的子部门
        List<DepartModel> list = departRepository.findByTenantIdAndParentIdIn(tenantId, departIds);
        if (!list.isEmpty()) {
            departModelList.addAll(list);
        }
        return buildDepartTree(departModelList, path);
    }

    /**
     * 根据租户ID和部门ID获取部门模型列表和部门ID列表
     * 此方法用于查询特定租户下属于特定父部门的所有部门信息，并将这些信息添加到给定的部门模型列表和部门ID列表中
     *
     * @param tenantId        租户ID，用于区分不同的租户
     * @param departId        部门ID，表示要查询的父部门
     * @param departModelList 部门模型列表，用于存储查询到的部门信息
     * @param departIds       部门ID列表，用于存储查询到的部门ID
     */
    public void getDepartModelListAndIdsList(Long tenantId, Long departId, List<DepartModel> departModelList, List<Long> departIds) {
        departRepository.findByTenantIdAndParentId(tenantId, departId).forEach(departModel -> {
            departModelList.add(departModel);
            departIds.add(departModel.getId());
        });
    }

    /**
     * 构建部门树
     * 根据部门模型列表构建一个部门树结构，用于展示 hierarchical 数据结构
     *
     * @param departModelList 部门模型列表，包含所有部门的信息
     * @param parentPath      父部门的路径，用于构建部门的层级路径
     * @return 返回构建好的部门树列表
     */
    private List<DepartAndKbListResp> buildDepartTree(List<DepartModel> departModelList, String parentPath) {
        List<DepartAndKbListResp> result = new ArrayList<>();
        if (null != departModelList) {
            Map<Long, DepartAndKbListResp> map = new HashMap<>();
            Iterator<DepartModel> iterator = departModelList.iterator();
            while (iterator.hasNext()) {
                DepartModel departModel = iterator.next();
                if (map.containsKey(departModel.getParentId())) {
                    DepartAndKbListResp parent = map.get(departModel.getParentId());
                    DepartAndKbListResp child = new DepartAndKbListResp(departModel.getId(), departModel.getDepartName()
                            , parentPath + pathSeparator + parent.getId() + pathSeparator + departModel.getId()
                            , true, new ArrayList<>());
                    parent.getChildDepartRespList().add(child);
                } else {
                    DepartAndKbListResp depart = new DepartAndKbListResp(departModel.getId(), departModel.getDepartName()
                            , parentPath + pathSeparator + departModel.getId()
                            , true, new ArrayList<>());
                    map.put(departModel.getId(), depart);
                }
            }
            Set<Map.Entry<Long, DepartAndKbListResp>> eSet = map.entrySet();
            Iterator<Map.Entry<Long, DepartAndKbListResp>> entryIterator = eSet.iterator();
            while (entryIterator.hasNext()) {
                result.add(entryIterator.next().getValue());
            }
        }
        return result;
    }

    /**
     * 递归查询指定部门 ID 下的所有子部门，包括多层级的子部门。
     *
     * @param deptId 当前部门的 ID
     * @return 包含所有子部门 ID 的列表，包括多层级子部门
     */
    private List<Long> findAllSubDepartments(Long deptId) {
        return departRepository.selectSubById(deptId);
    }

    /**
     * 根据部门 ID 和租户 ID 查询部门信息，并递归查询该部门的所有下级部门。
     * 同时进行权限校验，确保用户只能查询自己所属租户和有权限的部门。
     *
     * @param tenantId 租户 ID
     * @param deptId   部门 ID。如果为 0，则查询公司层级的部门信息
     * @return 符合条件的部门信息列表
     */
    @Override
    public List<DepartResp> getDepartmentsByTenantAndParentId(Long tenantId, Long deptId) {
        // 1. 获取当前登录用户的租户 ID，确保用户只能查询自己所属租户的数据
        Long currentTenantId = LoginContextHolder.getLoginUserTenantId();

        // 2. 校验当前用户是否有权限查询该租户的部门信息
        if (!currentTenantId.equals(tenantId)) {
            // 如果当前用户的租户 ID 与传入的租户 ID 不一致，抛出无权访问异常
            throw ServiceExceptionUtil.exception(FORBIDDEN, "无权查询该租户的部门信息");
        }

        // 3. 如果部门 ID 为 0，表示查询公司层级的部门（没有上级部门的部门）
        if (deptId == 0) {
            // 查询属于该租户且没有上级部门（parent_id 为 null 或 0）的部门
            return departRepository.findByTenantIdAndParentIdNull(tenantId).stream()
                    // 将查询结果转换为 DepartResp 响应对象
                    .map(depart -> {
                                DepartResp departResp = new DepartResp();
                                departResp.setDepartName(depart.getDepartName());
                                departResp.setId(depart.getId());
                                return departResp;
                            }
                    )
                    .collect(Collectors.toList());
        }

        // 4. 查询当前部门及其所有下级部门的部门信息
        List<Long> deptIds = findAllSubDepartments(deptId);  // 查询下级部门的 ID 列表
        deptIds.add(deptId); // 将当前部门 ID 也加入到列表中

        // 5. 查询指定部门范围内的部门信息
        return departRepository.findByTenantIdAndIdIn(tenantId, deptIds).stream()
                // 将查询结果转换为 DepartResp 响应对象
                .map(depart -> {
                    DepartResp departResp = new DepartResp();
                    departResp.setDepartName(depart.getDepartName());
                    departResp.setId(depart.getId());
                    return departResp;
                })
                .collect(Collectors.toList());
    }

    @Override
    public Set<Long> getAccessibleDepartmentIds() {
        QDepartModel departModel = QDepartModel.departModel;

        // 1. 获取当前用户的部门 ID 和租户 ID
        Long userDeptId = Optional.ofNullable(LoginContextHolder.getLoginUserDepartId())
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UNAUTHORIZED, "用户部门信息未找到"));
        Long tenantId = Optional.ofNullable(LoginContextHolder.getLoginUserTenantId())
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UNAUTHORIZED, "用户租户信息未找到"));

        // 2. 查询公司层级的根部门 ID（父 ID 为 0 且租户 ID 相同）
        List<Long> rootDeptIds = queryFactory.select(departModel.id)
                .from(departModel)
                .where(departModel.parentId.eq(0L)
                        .and(departModel.tenantId.eq(tenantId)))
                .fetch();

        // 3. 递归查询用户所在部门及其下级部门 ID
        List<Long> accessibleDeptIds = queryFactory.select(departModel.id)
                .from(departModel)
                .where(departModel.tenantId.eq(tenantId)
                        .and(departModel.parentId.in(userDeptId).or(departModel.id.eq(userDeptId))))
                .fetch();

        // 4. 合并所有部门 ID，并返回结果
        Set<Long> allDeptIds = new HashSet<>(rootDeptIds);
        allDeptIds.addAll(accessibleDeptIds);

        return allDeptIds;
    }

    /**
     * 根据部门ID筛选子部门树
     * <p>
     * 该方法通过递归遍历部门树列表，寻找与给定部门ID匹配的部门树或包含该ID的子树，并将其添加到结果列表中
     *
     * @param departId    部门ID，用于筛选部门树
     * @param departTrees 部门树列表，作为筛选的原始数据
     * @return 返回包含指定部门及其所有子部门的部门树列表
     */
    public Tree<String> subTree(Long departId, List<Tree<String>> departTrees) {
        // 检查传入的部门树列表是否不为空
        if (departTrees != null && (!departTrees.isEmpty())) {
            // 遍历部门树列表
            for (Tree<String> departTree : departTrees) {
                // 判断当前部门树的ID是否与给定的部门ID匹配
                if (departTree.getId().equals(departId.toString())) {
                    // 如果匹配，则将该部门树添加到结果列表中
                    return departTree;
                }
                Tree<String> subTree = subTree(departId, departTree.getChildren());
                if (subTree != null) {
                    return subTree;
                }
            }
        }
        // 返回结果列表，包含所有匹配的部门树和子树
        return null;
    }


    /**
     * 加载所有公司的部门树
     * 此方法的目的是遍历部门映射，并为每个部门加载其树结构
     * 部门树结构有助于在界面中以层次结构的方式展示部门及其子部门
     */
    @Override
    public void loadAllTenantDepartTrees() {
        // 获取部门映射，其中键是部门ID，值是该部门下的子部门列表
        Map<Long, List<DepartModel>> departMap = getDepartMap();

        // 遍历部门映射，为每个部门加载其树结构
        departMap.forEach(this::loadTenantDepartTree);
    }

    /**
     * 获取按租户ID分组的部门映射
     * <p>
     * 此方法通过查询所有部门信息，并根据部门模型中的租户ID进行分组，
     * 将每个租户ID与其相关的部门列表存储在一个映射中，以便于后续处理和检索
     *
     * @return 返回一个映射，其中键是租户ID，值是该租户下的部门列表
     */
    private Map<Long, List<DepartModel>> getDepartMap() {
        // 初始化一个空的映射，用于存储租户ID与部门列表的键值对
        Map<Long, List<DepartModel>> departMap = new HashMap<>();

        // 遍历所有的部门信息
        departRepository.findAll().forEach(departModel -> {
            // 检查当前部门模型的租户ID是否已存在于映射中
            if (departMap.containsKey(departModel.getTenantId())) {
                // 如果存在，则将当前部门模型添加到该租户ID对应的部门列表中
                departMap.get(departModel.getTenantId()).add(departModel);
            } else {
                // 如果不存在，则创建一个新的部门列表，将当前部门模型添加到列表中，
                // 并以当前部门模型的租户ID作为键，将该列表作为值添加到映射中
                List<DepartModel> departModelList = new ArrayList<>();
                departModelList.add(departModel);
                departMap.put(departModel.getTenantId(), departModelList);
            }
        });

        // 返回填充好的部门映射
        return departMap;
    }

    /**
     * 加载所有部门树
     * <p>
     * 此方法遍历部门仓库中的所有部门模型，并为每个部门模型调用loadUserDepartTree方法
     * 目的是为了加载每个部门及其用户相关的部门树结构
     */
    @Override
    public void loadAllDepartTrees() {
        departRepository.findAllByParentIdNot(0L).forEach(departModel -> {
            loadDepartTree(departModel.getId(), departModel.getTenantId());
        });
    }

    /**
     * 获取租户的部门树
     * <p>
     * 本方法用于获取当前租户的部门树结构首先尝试从缓存中获取部门树，
     * 如果缓存中没有找到，则从数据库中加载并缓存起来
     *
     * @return 部门树列表，每个部门作为一个树节点
     */
    @Override
    public List<Tree<String>> getTenantDepartTree() {
        // 获取当前登录用户的租户ID
        Long tenantId = LoginContextHolder.getLoginUserTenantId();

        // 尝试从缓存中获取部门树

        Tree<String> departTree = (Tree<String>) cacheFactory.getHashCache().get(tenantId.toString());

        // 如果缓存中没有找到部门树，则从数据库中加载
        if (departTree == null) {
            departTree = loadTenantDepartTree(tenantId);
        }

        // 返回部门树列表
        return CollUtil.newArrayList(departTree);
    }


    /**
     * 获取部门树
     * <p>
     * 此方法用于构建和返回当前用户所在部门的树形结构
     * 它首先尝试从缓存中获取部门树，如果缓存中没有，则从数据库中加载并返回
     *
     * @return 部门树列表，每个树节点都是Tree<String>类型
     */
    @Override
    public List<Tree<String>> getDepartTree() {
        // 获取当前用户所在部门ID
        Long departId = LoginContextHolder.getLoginUserDepartId();
        // 获取当前用户所在租户ID
        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        // 从缓存中获取部门树
        Tree<String> children = (Tree<String>)cacheFactory.getHashCache().get(departId.toString());
        if (children == null) {
            // 如果缓存中没有部门树，则从数据库中加载
            children = loadDepartTree(departId, tenantId);
        }
        if(children.getParentId().equals("0")){
            return CollUtil.newArrayList(children);
        }
        Tree<String> parent = (Tree<String>)cacheFactory.getHashCache().get(tenantId.toString());
        if (parent == null) {
            parent = loadTenantDepartTree(tenantId);
        }
        Tree<String> result = new Tree<>();
        result.setId(parent.getId());
        result.setParentId(parent.getParentId());
        result.setName(parent.getName());
        result.setChildren(CollUtil.newArrayList(children));
        return CollUtil.newArrayList(result);
    }

    /**
     * 加载特定部门在特定租户下的部门树结构
     * 首先尝试从缓存中获取部门树，如果缓存中没有，则从数据库中加载并构建部门树
     *
     * @param departId 部门ID，用于加载该部门及其子部门的树结构
     * @param tenantId 租户ID，用于确保加载的是正确租户的数据
     * @return 返回一个部门树列表，每个树节点都是一个部门及其子部门的结构
     */
    private Tree<String> loadDepartTree(Long departId, Long tenantId) {
        // 尝试从缓存中获取当前租户的部门树
        Tree<String> tenantDepartTree = (Tree<String>) cacheFactory.getHashCache().get(tenantId.toString());
        if (tenantDepartTree == null) {
            // 如果缓存中没有，则加载当前租户的部门树
            tenantDepartTree = loadTenantDepartTree(tenantId);
        }
        List<Tree<String>> tenantDepartTrees = CollUtil.newArrayList();
        tenantDepartTrees.add(tenantDepartTree);
        Tree<String> result = subTree(departId, tenantDepartTrees);
        // 将加载的部门树放入缓存，以便下次快速访问
        cacheFactory.getHashCache().put(departId.toString(), result);

        return result;
    }

    private void loadDepartTrees(List<Long> departIds, Long tenantId) {
        for (Long departId : departIds) {
            loadDepartTree(departId, tenantId);
        }
    }


    /**
     * 初次加载部门树
     * <p>
     * 根据租户ID和部门列表生成部门树，并将生成的部门树缓存起来
     *
     * @param tenantId        租户ID，用于区分不同租户的数据
     * @param departModelList 部门模型列表，包含需要构建树结构的部门信息
     * @return 返回构建好的部门树列表
     */
    public Tree<String> loadTenantDepartTree(Long tenantId, List<DepartModel> departModelList) {
        if(departModelList == null){
            throw ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED,"该用户所在公司部门为空");
        }
        // 根据部门列表生成部门树
        Tree<String> tree = getDepartTree(departModelList);
        // 将生成的部门树放入缓存，以便后续快速访问
        cacheFactory.getHashCache().put(tenantId.toString(), tree);
        // 返回构建好的部门树
        return tree;
    }

    /**
     * 加载指定部门树
     * 根据租户ID获取部门列表，并将其转换为树形结构，然后缓存该树形结构
     *
     * @param tenantId 租户ID，用于查询部门信息
     * @return 部门树形结构列表
     */
    public Tree<String> loadTenantDepartTree(Long tenantId) {
        // 根据租户ID查询所有部门信息
        List<DepartModel> departModelList = departRepository.findByTenantId(tenantId);

        // 将查询到的部门信息列表转换为树形结构
        Tree<String> tree = getDepartTree(departModelList);

        // 将生成的部门树形结构缓存，以便下次快速访问
        cacheFactory.getHashCache().put(tenantId.toString(), tree);

        // 返回部门树形结构
        return tree;
    }

    /**
     * 构建部门树形结构
     *
     * @param departModelList 部门模型列表，用于构建树形结构
     * @return 返回一个表示部门层级关系的树形结构列表
     */
    private Tree<String> getDepartTree(List<DepartModel> departModelList) {
        if(departModelList.isEmpty()){
            return new Tree<>();
        }
        // 创建一个空的节点列表，用于存储所有部门对应的树节点
        List<TreeNode> nodeList = CollUtil.newArrayList();
        // 获取部门模型列表的迭代器，用于遍历每个部门模型
        Iterator<DepartModel> iterator = departModelList.iterator();

        // 遍历部门模型列表
        while (iterator.hasNext()) {
            // 获取当前迭代的部门模型
            DepartModel departModel = iterator.next();

            // 创建一个树节点对象，包含部门的ID、父部门ID、部门名称和排序信息
            TreeNode<String> treeNode =
                    new TreeNode(
                            departModel.getId(),
                            departModel.getParentId(),
                            departModel.getDepartName(),
                            departModel.getSort());

            // 创建一个哈希映射，用于存储额外的节点信息
            HashMap<String, Object> hashMap = new HashMap<>();
            // 将哈希映射设置为树节点的额外信息
            treeNode.setExtra(hashMap);

            // 将创建的树节点添加到节点列表中
            nodeList.add(treeNode);
        }

        // 使用节点列表构建树形结构，并返回树形结构列表
        return TreeNodeUtils.buildTree(nodeList).get(0);
    }

}