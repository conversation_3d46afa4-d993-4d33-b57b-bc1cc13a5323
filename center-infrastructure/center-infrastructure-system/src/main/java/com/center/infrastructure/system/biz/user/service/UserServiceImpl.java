package com.center.infrastructure.system.biz.user.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.center.cache.factory.CacheFactory;
import com.center.cache.interfaces.Cache;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.PageResult;
import com.center.framework.web.pojo.SortProperties;
import com.center.infrastructure.system.biz.depart.persistence.DepartRepository;
import com.center.infrastructure.system.biz.depart.persistence.QDepartModel;
import com.center.infrastructure.system.biz.depart.pojo.DepartResp;
import com.center.infrastructure.system.biz.role.enumerate.RoleEnum;
import com.center.infrastructure.system.biz.role.persistence.*;
import com.center.infrastructure.system.biz.role.pojo.RoleResp;
import com.center.infrastructure.system.biz.tenant.persitence.QTenantModel;
import com.center.infrastructure.system.biz.tenant.pojo.TenantResp;
import com.center.infrastructure.system.biz.tenant.service.TenantService;
import com.center.infrastructure.system.biz.user.persistence.*;
import com.center.infrastructure.system.biz.user.pojo.*;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j

public class UserServiceImpl implements UserService {
    @Resource
    private UserRepository userRepository;

    @Resource
    private UserRoleRepository userRoleRepository;

    @Resource
    private RoleRepository roleRepository;

    @Resource
    private DepartRepository departRepository;

    @Resource
    private TenantService tenantService;

    @Resource
    private JPAQueryFactory queryFactory;

    @Resource
    private CacheFactory cacheFactory;

    @Value("${center.jwt.expire-in}")
    private Long expireIn;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    @Value("${admin.password}")
    private String defaultPassword;

    @Override
    @Transactional
    public void register(UserRegisterReq userRegisterReq) {
        //1.先做重复性校验(姓名可以重复)
        checkPhoneNumber(userRegisterReq.getPhoneNumber());

        //2.设置数据库数据
        UserModel userModel = OrikaUtils.convert(userRegisterReq, UserModel.class);
        userModel.setPassword(defaultPassword);
        userModel.setStatus(CommonStatusEnum.ACTIVE);
        userModel.setDisplayName(getFullName(userRegisterReq.getUserLastName(), userRegisterReq.getUserFirstName()));
        userModel.setUsername(getFullName(userRegisterReq.getUserLastName(), userRegisterReq.getUserFirstName()));
        userModel.setDepartId(0L);
        userModel.setDeleted(false);

        //3.存入数据库
        userRepository.save(userModel);
    }


    @Override
    @Transactional
    public void saveUsername(UsernameUpdateReq usernameUpdateReq) {
        //1.检查名称和姓氏是否为空
        if (!StringUtils.isBlank(usernameUpdateReq.getUserFirstName()) || !StringUtils.isBlank(usernameUpdateReq.getUserLastName())) {
            UserModel userModel = getUserModel(usernameUpdateReq.getUserId());
            userModel.setUserLastName(usernameUpdateReq.getUserLastName());
            userModel.setUserFirstName(usernameUpdateReq.getUserFirstName());
            userModel.setUsername(usernameUpdateReq.getUserLastName() + usernameUpdateReq.getUserFirstName());
            userModel.setDisplayName(usernameUpdateReq.getUserLastName() + usernameUpdateReq.getUserFirstName());

            //2.设置缓存内容
            UserModel modelInfo = userRepository.save(userModel);
            UserInfo userInfo = OrikaUtils.convert(modelInfo, UserInfo.class);
            Cache cache = cacheFactory.getHashCache();
            cache.put(LoginContextHolder.getLoginUserId().toString(), userInfo, expireIn);
        } else
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "参数不能为空");
    }

    @Override
    @Transactional
    public void saveIdentityNumber(IdentityNumberReq identityNumberReq) {
        //1.先判断是否修改
        UserModel userModel = getUserModel(identityNumberReq.getUserId());
        if (!StringUtils.equals(userModel.getIdentityNumber(), identityNumberReq.getIdentityNumber())) {
            userModel.setIdentityNumber(identityNumberReq.getIdentityNumber());

            //2.设置缓存内容
            UserModel modelInfo = userRepository.save(userModel);
            UserInfo userInfo = OrikaUtils.convert(modelInfo, UserInfo.class);
            Cache cache = cacheFactory.getHashCache();
            cache.put(LoginContextHolder.getLoginUserId().toString(), userInfo, expireIn);
        }
    }


    public String getFullName(String userLastName, String userFirstName) {
        if (StringUtils.isBlank(userLastName) || StringUtils.isBlank(userFirstName)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "姓氏或名字不能为空");
        }
        return userLastName + userFirstName;
    }


    @Override
    public UserResp get(Long id) {
        QUserModel qUserModel = QUserModel.userModel;
        QRoleModel qRoleModel = QRoleModel.roleModel;
        QUserRoleModel qUserRoleModel = QUserRoleModel.userRoleModel;
        QDepartModel qDepartModel = QDepartModel.departModel;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qUserModel.id.eq(id));
        builder.and(qUserRoleModel.userId.eq(id));
        builder.and(qRoleModel.id.eq(qUserRoleModel.roleId));
        builder.and(qDepartModel.id.eq(qUserModel.departId));
        UserResp userResp = queryFactory.select((Projections.bean(
                        UserResp.class,
                        qUserModel.id,
                        qUserModel.username,
                        qUserModel.displayName,
                        qUserModel.email,
                        qUserModel.phoneNumber,
                        qRoleModel.roleName,
                        qRoleModel.id.as("roleId"),
                        qDepartModel.id.as("departId")
                )))
                .from(qUserModel, qUserRoleModel, qDepartModel, qRoleModel)
                .where(builder)
                .fetchFirst();
        return userResp;
    }

    @Override
    public PageResult<UserPageView> page(UserPageReq req) {
        try {
            Long currentTenantId = LoginContextHolder.getLoginUserTenantId();
            Long currentUserId = LoginContextHolder.getLoginUserId();
            Pageable pageable = PageRequest.of(req.getPageNo() - 1
                    , req.getPageSize()
                    , Sort.by(Sort.Direction.DESC,
                            SortProperties.CREATE_TIME));
            String content = req.getContent();
            QUserRoleModel qUserRoleModel = QUserRoleModel.userRoleModel;
            QUserModel qUserModel = QUserModel.userModel;
            QRoleModel qRoleModel = QRoleModel.roleModel;
            QDepartModel qDepartModel = QDepartModel.departModel;
            QTenantModel qTenantModel = QTenantModel.tenantModel;
            BooleanBuilder builder = new BooleanBuilder();
            if (StrUtil.isNotEmpty(content)) {
                builder.and(qUserModel.username.contains(content))
                        .or(qUserModel.phoneNumber.contains(content));
            }
            builder.and(qUserModel.id.eq(qUserRoleModel.userId));
            builder.and(qUserRoleModel.roleId.eq(qRoleModel.id));
            builder.and(qUserModel.deleted.eq(false));
            builder.and(qUserModel.departId.eq(qDepartModel.id));
            builder.and(qUserModel.tenantId.eq(qTenantModel.id));
            // 超级管理员的特殊查询条件
            if (isSuperAdmin(currentUserId)) {
                builder.and(QRoleModel.roleModel.code.eq(RoleEnum.ADMIN)
                        .or(QRoleModel.roleModel.code.eq(RoleEnum.SUPERADMIN))
                );
            } else {
                builder.and(qUserModel.tenantId.eq(currentTenantId));
            }
            JPQLQuery<UserPageView> jpqlQuery = queryFactory.select((Projections.bean(
                            UserPageView.class,
                            qUserModel.id,
                            qUserModel.username,
                            qUserModel.displayName,
                            qUserModel.email,
                            qUserModel.phoneNumber,
                            qUserModel.status,
                            qDepartModel.departName,
                            qRoleModel.roleName,
                            qRoleModel.code,
                            qTenantModel.name.as("companyName")
                    )))
                    .from(qUserModel, qUserRoleModel, qDepartModel, qRoleModel, qTenantModel)
                    .orderBy(qRoleModel.sort.desc(), qUserModel.createTime.desc())
                    .offset(pageable.getOffset())
                    .limit(pageable.getPageSize())
                    .where(builder);
            Long total = jpqlQuery.fetchCount();
            List<UserPageView> list = jpqlQuery.fetch();
            if (!isSuperAdmin(currentUserId)) {
                for (UserPageView userPageView : list) {
                    if (userPageView.getCode().equals(RoleEnum.ADMIN)) {
                        userPageView.setStatus(null);
                        userPageView.setDepartName(null);
                        break;
                    }
                }
            }
            return PageResult.of(list, total);
        } catch (IllegalArgumentException e) {
            log.error("查询参数错误", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.GET_OBJECT_ERROR, e, "获取用户信息列表失败");
        } catch (Exception e) {
            log.error("获取列表失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.GET_OBJECT_ERROR, e, "获取用户信息列表失败");
        }
    }

    @Override
    @Transactional
    public void save(UserCreateReq userCreateReq) {
        Long currentTenantId = LoginContextHolder.getLoginUserTenantId();
        checkTenant(currentTenantId);
        checkUsername(userCreateReq.getUsername(), userCreateReq.getTenantId());
        checkPhoneNumber(userCreateReq.getPhoneNumber());
        UserModel userModel = OrikaUtils.convert(userCreateReq, UserModel.class);
        userModel.setTenantId(currentTenantId);
        userModel.setPassword(defaultPassword);
        userModel.setDeleted(false);
        userModel.setStatus(CommonStatusEnum.ACTIVE);
        userRepository.save(userModel);
        if (roleRepository.findById(userCreateReq.getRoleId()).isPresent()) {
            UserRoleCreateReq userRoleCreateReq = new UserRoleCreateReq();
            userRoleCreateReq.setUserId(userModel.getId());
            userRoleCreateReq.setRoleId(userCreateReq.getRoleId());
            userRoleRepository.save(OrikaUtils.convert(userRoleCreateReq, UserRoleModel.class));
        } else
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "角色不存在");
    }

    @Override
    @Transactional
    public void update(UserUpdateReq userUpdateReq) {
        UserModel originalUserModel = getUserModel(userUpdateReq.getId());
        checkUsername(originalUserModel, userUpdateReq.getUsername(), userUpdateReq.getTenantId());
        checkPhoneNumber(originalUserModel, userUpdateReq.getPhoneNumber());
        if (CommonStatusEnum.INACTIVE.getValue().equals(originalUserModel.getStatus())
                && CommonStatusEnum.ACTIVE.getValue().equals(userUpdateReq.getStatus())) {
            checkTenant(userUpdateReq.getTenantId());
        }
        if (!isAdmin(originalUserModel.getId())) {
            UserModel userModel = OrikaUtils.convert(userUpdateReq, UserModel.class);
            userModel.setPassword(userUpdateReq.getMD5Password());
            userRepository.save(userModel);
            log.info("保存用户成功");
            UserRoleModel userRoleModel = userRoleRepository.findByUserId(userModel.getId());
            UserRoleUpdateReq userRoleUpdateReq = OrikaUtils.convert(userRoleModel, UserRoleUpdateReq.class);
            userRoleUpdateReq.setUserId(userModel.getId());
            userRoleUpdateReq.setRoleId(userUpdateReq.getRoleId());
            userRoleRepository.save(OrikaUtils.convert(userRoleUpdateReq, UserRoleModel.class));
            log.info("保存用户角色成功");
            saveLoginInfo();
        } else
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "超级管理员和系统管理员不能被修改");
    }

    @Override
    @Transactional
    public void delete(Long id) {
        try {
            UserModel userModel = userRepository.findById(id).orElseThrow(() ->
                    ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "此用户不存在"));
            if (!isAdmin(id) && !isSuperAdmin(id)) {
                userModel.setDeleted(true);
                userModel.setUpdateTime(LocalDateTime.now());
                userRepository.save(userModel);
            } else
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "超级管理员和系统管理员不能被删除");
        } catch (EmptyResultDataAccessException e) {
            log.error("删除节点出错！", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, e, "此用户不存在");
        }
    }

    @Override
    public UserResp getByUsernameAndPassword(String username, String password) {
        UserModel userModel = userRepository.findByUsernameAndPassword(username, SecureUtil.md5(password));
        if (null != userModel) {
            return OrikaUtils.convert(userModel, UserResp.class);
        }
        return null;
    }

    @Override
    public UserResp getByPhoneNumberAndPassword(String phoneNumber, String password) {
        UserModel userModel = userRepository.findByPhoneNumberAndPassword(phoneNumber, SecureUtil.md5(password));
        if (null != userModel) {
            return OrikaUtils.convert(userModel, UserResp.class);
        }
        return null;
    }

    @Override
    public void updateUserLoginInfo(Long id, String ip) {
        if (StringUtils.isEmpty(ip)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY, "IP地址不能为空");
        }
        UserModel userModel = getUserModel(id);
        userModel.setLoginIp(ip);
        userModel.setLoginTime(LocalDateTime.now());
        userRepository.save(userModel);
    }

    @Override
    public void setPassword(PasswordReq passwordReq) {
        UserModel userModel = getUserModel(LoginContextHolder.getLoginUserId());

        if (userModel.getPassword().equals(SecureUtil.md5(passwordReq.getPassword()))) {
            if (passwordReq.getNewPassword().equals(passwordReq.getNewPassword2())) {
                userModel.setPassword(SecureUtil.md5(passwordReq.getNewPassword()));
            } else
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "两次密码不一致");
        } else
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "原密码不正确");
        userRepository.save(userModel);
    }

    @Override
    public void resetPassword(Long id) {
        UserModel userModel = userRepository.findById(id).orElseThrow(() ->
                ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "此用户不存在"));
        if (!isSuperAdmin(id)) {
            userModel.setPassword(defaultPassword);
            userRepository.save(userModel);
        } else
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "超级管理员密码不能重置");
    }

    @Override
    public void updateStatus(Long id, CommonStatusEnum commonStatusEnum) {
        UserModel userModel = userRepository.findById(id).orElseThrow(() ->
                ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "此用户不存在"));
        if (isAdmin(id) || isSuperAdmin(id)) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "管理员不能改变状态");
        }
        if (!userModel.getStatus().equals(commonStatusEnum)) {
            userModel.setStatus(commonStatusEnum);
            userRepository.save(userModel);
        } else throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INVALID_CONTENT, "用户状态错误");
    }

    @Override
    public List<RoleResp> getRole() {
        Long currentTenantId = LoginContextHolder.getLoginUserTenantId();
        List<RoleModel> roleModelList = roleRepository.findByTenantIdAndStatus(currentTenantId, CommonStatusEnum.ACTIVE);
        return roleModelList.stream()
                .filter(roleModel -> !(RoleEnum.ADMIN == roleModel.getCode()))
                .map(roleModel ->
                        OrikaUtils.convert(roleModel, RoleResp.class)).collect(Collectors.toList());
    }

    @Override
    public List<DepartResp> getDepart() {
        Long currentTenantId = LoginContextHolder.getLoginUserTenantId();
        return departRepository.findByTenantId(currentTenantId).stream().map(departModel ->
                OrikaUtils.convert(departModel, DepartResp.class)).collect(Collectors.toList());
    }

    @Override
    public void saveLoginInfo() {
        QUserModel qUserModel = QUserModel.userModel;
        QUserRoleModel qUserRoleModel = QUserRoleModel.userRoleModel;
        QRoleModel qRoleModel = QRoleModel.roleModel;
        QTenantModel qTenantModel = QTenantModel.tenantModel;
        QDepartModel qDepartModel = QDepartModel.departModel;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qUserModel.id.eq(LoginContextHolder.getLoginUserId()));
        builder.and(qUserRoleModel.userId.eq(qUserModel.id));
        builder.and(qRoleModel.id.eq(qUserRoleModel.roleId));
        builder.and(qTenantModel.id.eq(qUserModel.tenantId));
        builder.and(qDepartModel.id.eq(qUserModel.departId));
        UserInfo userInfo = jpaQueryFactory.select(Projections.bean(
                        UserInfo.class,
                        qUserModel.id.as("userId"),
                        qUserModel.username.as("userName"),
                        qUserModel.displayName,
                        qUserModel.phoneNumber,
                        qUserModel.email,
                        qUserModel.deleted.as("isDeleted"),
                        qRoleModel.id.as("roleId"),
                        qRoleModel.code,
                        qRoleModel.status.as("roleStatus"),
                        qRoleModel.remark,
                        qUserModel.tenantId,
                        qTenantModel.name.as("tenantName"),
                        qTenantModel.description.as("tenantDescription"),
                        qTenantModel.status.as("tenantStatus"),
                        qUserModel.departId,
                        qDepartModel.departName))
                .from(qUserModel, qRoleModel, qUserRoleModel, qTenantModel, qDepartModel)
                .where(builder)
                .fetchFirst();
        Cache cache = cacheFactory.getHashCache();
        cache.put(LoginContextHolder.getLoginUserId().toString(), userInfo, expireIn);
    }

    private UserModel getUserModel(Long id) {
        Optional<UserModel> option = userRepository.findById(id);
        if (option.isPresent()) {
            return option.get();
        } else {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "此用户不存在");
        }
    }

    private void checkTenant(Long tenantId) {
        TenantResp tenantResp = tenantService.get(tenantId);
        if (tenantResp == null) {
            throw ServiceExceptionUtil
                    .exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "租户不存在");
        }
        if (LocalDateTime.now().isAfter(tenantResp.getExpireTime())) {
            throw ServiceExceptionUtil
                    .exception(GlobalErrorCodeConstants.INACTIVE_OBJECT, "租户已失效");
        }
        Long accountCount = userRepository.countByTenantIdAndStatus(tenantId, CommonStatusEnum.ACTIVE);
        if (accountCount >= tenantResp.getAccountCount()) {
            throw ServiceExceptionUtil
                    .exception(GlobalErrorCodeConstants.OVER_LIMIT, "租户下的激活用户数量超过最大限制");
        }
    }


    private void checkUsername(String username, Long tenantId) {
        UserModel userModel = userRepository.findByUsernameAndTenantId(username, tenantId);
        if (userModel != null) {
            throw ServiceExceptionUtil
                    .exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "重复的用户名");
        }
    }

    private void checkUsername(UserModel userModel, String username, Long tenantId) {
        if (null != userModel) {
            if (!username.equals(userModel.getUsername())) {
                userModel = userRepository.findByUsernameAndTenantId(username, tenantId);
                if (userModel != null) {
                    throw ServiceExceptionUtil
                            .exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "重复的用户名");
                }
            }
        } else {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "此用户不存在");
        }
    }

    /**
     * 创建用户时检查手机号是否重复
     *
     * @param phoneNumber-手机号
     */
    private void checkPhoneNumber(String phoneNumber) {
        UserModel userModel = userRepository.findByPhoneNumber(phoneNumber);
        if (userModel != null) {
            throw ServiceExceptionUtil
                    .exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "重复的手机号");
        }
    }


    /**
     * 判断更新后的手机号是否重复
     *
     * @param userModel-原来的用户信息
     * @param phoneNumber-手机号
     */
    private void checkPhoneNumber(UserModel userModel, String phoneNumber) {
        if (null != userModel) {
            if (!phoneNumber.equals(userModel.getPhoneNumber())) {
                userModel = userRepository.findByPhoneNumber(phoneNumber);
                if (userModel != null) {
                    throw ServiceExceptionUtil
                            .exception(GlobalErrorCodeConstants.DUPLICATED_OBJECT, "重复的手机号");
                }
            }
        } else {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "此用户不存在");
        }
    }

    /**
     * 根据id获取角色信息
     *
     * @param userId
     * @return
     */
    private RoleModel getRoleModel(Long userId) {
        UserRoleModel userRoleModel = userRoleRepository.findByUserId(userId);
        return roleRepository.findById(userRoleModel.getRoleId()).orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "角色不存在"));
    }

    @Override
    public Boolean isSuperAdmin(Long userId) {
        return RoleEnum.SUPERADMIN == getRoleModel(userId).getCode();
    }

    @Override
    public Boolean isAdmin(Long userId) {
        return RoleEnum.ADMIN == getRoleModel(userId).getCode();
    }
}
