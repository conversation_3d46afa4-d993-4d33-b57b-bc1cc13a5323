package com.center.infrastructure.system.biz.tenant.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public class TenantCreateReq{

    @Schema(description = "企业名称")
    @NotNull(message = "企业名称不能为空")
    @Length(min = 2, max = 20, message = "企业名称长度为 2-20 位")
    private String name;

    @Schema(description = "企业管理员姓名")
    @NotNull(message = "企业管理员姓名不能为空")
    @Length(min = 2, max = 20, message = "用户名长度为 2-20 位")
    private String managerName;

    @Schema(description = "企业管理员手机号")
    @Pattern(regexp = "^\\d{11}$",message = "请输入11为数字手机号")
    @NotNull(message = "企业管理员手机号不能为空")
    private String phoneNumber;

    @Schema(description = "联系邮箱")
    @Email(message = "邮箱格式不正确")
    private String email;

}
